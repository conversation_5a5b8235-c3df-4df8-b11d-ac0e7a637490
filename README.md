# MPPET-RLE: Multi-Person Pose Estimation and Tracking with Residual Log-likelihood Estimation

This repository implements MPPET-RLE, a novel approach for multi-person pose estimation and tracking that leverages the uncertainty information from Residual Log-likelihood Estimation (RLE) for robust temporal association.

## Overview

MPPET-RLE extends the RLE pose estimation framework to multi-person scenarios by:

1. **Person Detection**: Using Faster R-CNN for robust person detection
2. **RLE Pose Estimation**: Estimating both keypoint positions (μ) and uncertainties (σ) for each detected person
3. **Distribution-Aware Tracking**: Using pose uncertainties for robust frame-to-frame association via probabilistic motion models

## Key Features

- **Uncertainty-Aware Pose Estimation**: Uses RLE to predict both keypoint locations and their confidence distributions
- **Robust Tracking**: Leverages pose uncertainties to weight keypoints in association, down-weighting unreliable keypoints
- **Kalman Filter Integration**: Incorporates pose uncertainties into motion prediction for better tracking
- **PoseTrack21 Support**: Full evaluation pipeline for PoseTrack21 dataset
- **Comprehensive Metrics**: MOTA, MOTP, HOTA, AP_pose evaluation

## Architecture

```
Input Video Frame
       ↓
Person Detection (Faster R-CNN)
       ↓
RLE Pose Estimation (per person)
   ↓           ↓
Pose μ      Pose σ (uncertainty)
       ↓
Distribution-Aware Association
       ↓
Kalman Filter Update
       ↓
Tracking Results
```

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd pktrack
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Download PoseTrack21 dataset:
```bash
cd datasets/PoseTrack21
bash download.sh
```

## Dataset Structure

```
datasets/PoseTrack21/
├── data/
│   ├── images/
│   │   ├── train/
│   │   ├── val/
│   │   └── test/
│   └── posetrack_data/
│       ├── train/
│       └── val/
└── eval/
```

## Usage

### Training

Train the MPPET-RLE model:

```bash
python train_mppet.py \
    --data_root datasets/PoseTrack21/data \
    --batch_size 8 \
    --epochs 100 \
    --lr 1e-4 \
    --save_dir checkpoints \
    --log_dir logs
```

Key training arguments:
- `--data_root`: Path to PoseTrack21 dataset
- `--batch_size`: Training batch size
- `--epochs`: Number of training epochs
- `--lr`: Learning rate
- `--weight_decay`: Weight decay for regularization
- `--resume`: Resume from checkpoint

### Evaluation

Evaluate on PoseTrack21 validation set:

```bash
python eval_mppet.py \
    --checkpoint checkpoints/best_model.pth \
    --data_root datasets/PoseTrack21/data \
    --split val \
    --eval_detection \
    --eval_tracking \
    --output_dir evaluation_results
```

Evaluation options:
- `--eval_detection`: Evaluate detection and pose estimation
- `--eval_tracking`: Evaluate tracking performance
- `--create_demo`: Create demo video with tracking results

### Demo

Run inference on video:

```bash
python demo.py \
    --checkpoint checkpoints/best_model.pth \
    --input path/to/video.mp4 \
    --output output_video.mp4 \
    --save_results tracking_results.json
```

## Model Components

### 1. RLE Pose Estimation (`RLE_pose.py`)

- **Backbone**: ResNet-50 with ImageNet pretraining
- **Flow Model**: RealNVP for learning pose distributions
- **Outputs**: 
  - `pred_jts`: Keypoint positions μ (B, 17, 2)
  - `sigma`: Keypoint uncertainties σ (B, 17, 2)
  - `maxvals`: Confidence scores derived from σ

### 2. Person Detection (`detection/detector.py`)

- **Architecture**: Faster R-CNN with ResNet-50-FPN backbone
- **Classes**: Background + Person
- **Post-processing**: NMS with configurable thresholds

### 3. Tracking System (`tracking/`)

#### Motion Model (`motion_model.py`)
- **Kalman Filter**: Incorporates pose uncertainties as measurement noise
- **State**: Joint positions, velocities, and bounding box parameters
- **Uncertainty Propagation**: Uses σ values to weight observations

#### Association (`association.py`)
- **Distribution-Aware**: Uses pose uncertainties to weight keypoint distances
- **Cost Function**: Combines bounding box IoU and weighted pose similarity
- **Hungarian Algorithm**: Optimal assignment between tracks and detections

#### Track Management (`track_manager.py`)
- **Track States**: Tentative, Confirmed, Deleted
- **Lifecycle**: Initialization, update, termination based on hit streaks
- **History**: Maintains pose and uncertainty history for each track

### 4. Visualization (`utils/visualization.py`)

- **Pose Rendering**: Draw keypoints with uncertainty ellipses
- **Track Visualization**: Color-coded tracks with IDs
- **Uncertainty Plots**: Distribution analysis across joints

### 5. Metrics (`utils/metrics.py`)

- **Pose Metrics**: AP_pose, PCK, OKS
- **Tracking Metrics**: MOTA, MOTP, ID switches, fragmentations
- **MOTMetrics Integration**: Standard evaluation protocols

## Configuration

### Model Configuration

```python
# Pose estimation config
pose_config = {
    'NUM_LAYERS': 50,
    'NUM_FC_FILTERS': [2048, 1024],
    'PRESET': {
        'NUM_JOINTS': 17,
        'IMAGE_SIZE': [256, 192]
    }
}

# Detection config
detection_config = {
    'pretrained': True,
    'num_classes': 2,
    'score_threshold': 0.5,
    'nms_threshold': 0.5
}

# Tracking config
tracking_config = {
    'association_type': 'distribution_aware',
    'motion_model': 'kalman',
    'max_age': 30,
    'min_hits': 3,
    'bbox_weight': 0.3,
    'pose_weight': 0.7,
    'uncertainty_threshold': 10.0
}
```

## Key Innovations

### 1. Distribution-Aware Association

Traditional tracking relies on simple distance metrics. MPPET-RLE uses uncertainty information:

```python
# Weighted pose distance
weights = 1.0 / (sigma_magnitude + 1e-6)
weighted_distance = np.sum(distances * weights)
```

### 2. Uncertainty-Informed Motion Model

Kalman filter measurement noise is adapted based on pose uncertainties:

```python
# Update measurement noise based on pose uncertainty
for i in range(num_joints):
    self.kf.R[i*2, i*2] = max(pose_sigma[i, 0], 1.0)
    self.kf.R[i*2+1, i*2+1] = max(pose_sigma[i, 1], 1.0)
```

### 3. Confidence-Based Filtering

Low-confidence keypoints are down-weighted in association:

```python
# Filter uncertain keypoints
uncertain_mask = sigma_magnitude > uncertainty_threshold
weights[uncertain_mask] = 0.1
```

## Results

Expected performance on PoseTrack21:

| Metric | Value |
|--------|-------|
| MOTA | ~65% |
| MOTP | ~80% |
| AP_pose | ~70% |
| ID Switches | <100 |

## Troubleshooting

### Common Issues

1. **CUDA Out of Memory**: Reduce batch size or use gradient accumulation
2. **Import Errors**: Ensure all dependencies are installed
3. **Dataset Path**: Verify PoseTrack21 dataset structure

### Performance Tips

1. **Multi-GPU Training**: Use `torch.nn.DataParallel` for multiple GPUs
2. **Mixed Precision**: Enable AMP for faster training
3. **Data Loading**: Increase `num_workers` for faster data loading

## Citation

If you use this code, please cite:

```bibtex
@article{mppet_rle_2024,
  title={MPPET-RLE: Multi-Person Pose Estimation and Tracking with Residual Log-likelihood Estimation},
  author={Your Name},
  journal={arXiv preprint},
  year={2024}
}
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- RLE pose estimation framework
- PoseTrack21 dataset
- MOTMetrics evaluation toolkit
- PyTorch and torchvision communities
