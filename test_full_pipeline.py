"""
Test the full MPPET-RLE pipeline including detection, pose estimation, and tracking
"""

import torch
import numpy as np
import cv2
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from mppet_rle import MPPET_RLE
from utils.visualization import draw_tracking_results
from utils.metrics import PoseTrackingMetrics


def test_full_pipeline():
    """Test the complete MPPET-RLE pipeline."""
    print("Testing Full MPPET-RLE Pipeline...")
    
    # Create model
    model = MPPET_RLE()
    model.eval()
    
    print(f"Model created successfully")
    print(f"Components:")
    print(f"  - Detector: {type(model.detector).__name__}")
    print(f"  - Pose Estimator: {type(model.pose_estimator).__name__}")
    print(f"  - Tracker: {type(model.tracker).__name__}")
    
    # Create dummy video sequence (5 frames)
    frames = []
    for i in range(5):
        frame = torch.randn(3, 480, 640)
        frames.append(frame)
    
    print(f"Created {len(frames)} dummy frames")
    
    # Process frames through the pipeline
    all_tracking_results = []
    
    with torch.no_grad():
        for frame_idx, frame in enumerate(frames):
            print(f"\nProcessing frame {frame_idx + 1}/{len(frames)}")
            
            try:
                # Detection mode - get detections with poses
                detection_results = model([frame], mode='detect')
                print(f"  Detection results: {len(detection_results[0])} detections")
                
                # Tracking mode - associate across frames
                tracking_results = model([frame], mode='track')
                print(f"  Tracking results: {len(tracking_results[0])} tracks")
                
                all_tracking_results.append(tracking_results[0])
                
                # Print track details
                for track in tracking_results[0]:
                    print(f"    Track ID: {track['track_id']}, Confidence: {track['confidence']:.3f}")
                
            except Exception as e:
                print(f"  Error processing frame {frame_idx}: {str(e)}")
                all_tracking_results.append([])
    
    print(f"\nPipeline completed successfully!")
    print(f"Processed {len(frames)} frames")
    
    # Test metrics calculation
    print("\nTesting metrics calculation...")
    metrics = PoseTrackingMetrics()
    
    # Create dummy ground truth and predictions for metrics
    for frame_idx in range(len(frames)):
        # Dummy GT
        gt_tracks = [
            {'track_id': 1, 'bbox': [100, 100, 200, 300]},
            {'track_id': 2, 'bbox': [300, 150, 400, 350]}
        ]
        
        # Use actual tracking results as predictions
        pred_tracks = all_tracking_results[frame_idx]
        
        metrics.update_tracking_metrics(gt_tracks, pred_tracks, frame_idx)
    
    # Get final metrics
    final_metrics = metrics.get_all_metrics()
    print("Final metrics:")
    for key, value in final_metrics.items():
        print(f"  {key}: {value}")
    
    return True


def test_visualization():
    """Test visualization with tracking results."""
    print("\nTesting visualization...")
    
    # Create dummy image
    image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
    
    # Create dummy tracking results
    tracking_results = [
        {
            'track_id': 1,
            'bbox': [100, 100, 200, 300],
            'pose': np.random.rand(17, 2) * 100 + 150,
            'pose_sigma': np.random.rand(17, 2) * 2 + 1,
            'confidence': 0.9
        },
        {
            'track_id': 2,
            'bbox': [300, 150, 400, 350],
            'pose': np.random.rand(17, 2) * 100 + 350,
            'pose_sigma': np.random.rand(17, 2) * 2 + 1,
            'confidence': 0.8
        }
    ]
    
    # Draw tracking results
    vis_image = draw_tracking_results(image, tracking_results, show_ids=True, show_uncertainty=True)
    
    # Save visualization
    output_path = 'test_tracking_visualization.jpg'
    cv2.imwrite(output_path, cv2.cvtColor(vis_image, cv2.COLOR_RGB2BGR))
    print(f"Visualization saved to: {output_path}")
    
    return True


def test_model_loading():
    """Test loading a trained model."""
    print("\nTesting model loading...")
    
    checkpoint_path = 'checkpoints/best_model.pth'
    
    if os.path.exists(checkpoint_path):
        print(f"Loading checkpoint: {checkpoint_path}")
        
        # Create model
        model = MPPET_RLE()
        
        # Load checkpoint
        checkpoint = torch.load(checkpoint_path, map_location='cpu')
        
        # The checkpoint contains the pose estimator weights
        # We need to load them into the correct component
        pose_state_dict = {}
        for key, value in checkpoint['model_state_dict'].items():
            if key.startswith('pose_estimator.') or not key.startswith(('detector.', 'tracker.')):
                # Remove 'pose_estimator.' prefix if present
                new_key = key.replace('pose_estimator.', '') if key.startswith('pose_estimator.') else key
                pose_state_dict[new_key] = value
        
        # Load into pose estimator
        model.pose_estimator.load_state_dict(pose_state_dict, strict=False)
        
        print(f"Model loaded successfully from epoch {checkpoint.get('epoch', 'unknown')}")
        print(f"Training loss: {checkpoint.get('train_loss', 'unknown')}")
        
        # Test inference
        model.eval()
        with torch.no_grad():
            test_input = [torch.randn(3, 480, 640)]
            results = model(test_input, mode='detect')
            print(f"Inference test successful: {len(results[0])} detections")
        
        return True
    else:
        print(f"Checkpoint not found: {checkpoint_path}")
        print("Run training first with: python train_simple.py")
        return False


def main():
    """Run all pipeline tests."""
    print("=" * 60)
    print("MPPET-RLE Full Pipeline Test")
    print("=" * 60)
    
    tests = [
        ("Full Pipeline", test_full_pipeline),
        ("Visualization", test_visualization),
        ("Model Loading", test_model_loading)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                print(f"✓ {test_name} test passed")
                passed += 1
            else:
                print(f"✗ {test_name} test failed")
        except Exception as e:
            print(f"✗ {test_name} test failed with error: {str(e)}")
            import traceback
            traceback.print_exc()
    
    print(f"\n{'='*60}")
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! MPPET-RLE pipeline is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Check the implementation.")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
