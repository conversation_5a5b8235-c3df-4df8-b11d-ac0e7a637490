"""
Demo script for MPPET-RLE inference on video sequences
"""

import os
import torch
import cv2
import numpy as np
import argparse
from tqdm import tqdm
import json

from mppet_rle import MPPET_RLE
from utils.visualization import draw_tracking_results, draw_pose_with_uncertainty
from utils.metrics import PoseTrackingMetrics


def load_model(checkpoint_path, device):
    """Load trained model from checkpoint."""
    model = MPPET_RLE()
    
    if os.path.isfile(checkpoint_path):
        print(f'Loading checkpoint {checkpoint_path}')
        checkpoint = torch.load(checkpoint_path, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
        print(f'Loaded model from epoch {checkpoint.get("epoch", "unknown")}')
    else:
        print(f'Checkpoint {checkpoint_path} not found, using random weights')
    
    model = model.to(device)
    model.eval()
    
    return model


def preprocess_image(image, target_size=(640, 480)):
    """Preprocess image for model input."""
    # Resize image
    resized = cv2.resize(image, target_size)
    
    # Convert to RGB
    rgb_image = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)
    
    # Normalize
    normalized = rgb_image.astype(np.float32) / 255.0
    
    # Convert to tensor
    tensor = torch.from_numpy(normalized).permute(2, 0, 1).unsqueeze(0)
    
    return tensor, resized


def process_video(model, video_path, output_path, device, max_frames=None):
    """Process video and create output with tracking results."""
    # Open video
    cap = cv2.VideoCapture(video_path)
    
    if not cap.isOpened():
        raise ValueError(f"Cannot open video: {video_path}")
    
    # Get video properties
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    if max_frames:
        total_frames = min(total_frames, max_frames)
    
    print(f"Video properties: {width}x{height}, {fps} FPS, {total_frames} frames")
    
    # Setup video writer
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    # Reset tracker
    model.reset_tracker()
    
    # Process frames
    frame_count = 0
    all_tracking_results = []
    
    with torch.no_grad():
        pbar = tqdm(total=total_frames, desc="Processing video")
        
        while True:
            ret, frame = cap.read()
            
            if not ret or (max_frames and frame_count >= max_frames):
                break
            
            try:
                # Preprocess frame
                input_tensor, processed_frame = preprocess_image(frame)
                input_tensor = input_tensor.to(device)
                
                # Run inference (simplified)
                # In practice, this would run the full detection + pose + tracking pipeline
                
                # For demo purposes, create dummy tracking results
                dummy_tracking_results = []
                
                # Simulate some tracks
                if frame_count > 10:  # Start showing tracks after a few frames
                    for i in range(2):  # Simulate 2 people
                        # Create dummy bounding box
                        x = 100 + i * 200 + np.random.randint(-20, 20)
                        y = 100 + np.random.randint(-20, 20)
                        w, h = 80, 160
                        
                        # Create dummy pose
                        dummy_pose = np.random.rand(17, 2) * 50 + np.array([x + w//2, y + h//2])
                        dummy_sigma = np.random.rand(17, 2) * 2 + 1
                        
                        track_result = {
                            'track_id': i + 1,
                            'bbox': [x, y, x + w, y + h],
                            'pose': dummy_pose,
                            'pose_sigma': dummy_sigma,
                            'confidence': 0.8 + np.random.rand() * 0.2,
                            'frame_id': frame_count
                        }
                        dummy_tracking_results.append(track_result)
                
                all_tracking_results.append(dummy_tracking_results)
                
                # Draw tracking results
                output_frame = draw_tracking_results(
                    frame, dummy_tracking_results, 
                    show_ids=True, show_uncertainty=True
                )
                
                # Add frame info
                cv2.putText(output_frame, f'Frame: {frame_count}', (10, 30),
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                
                cv2.putText(output_frame, f'Tracks: {len(dummy_tracking_results)}', (10, 70),
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                
                # Write frame
                out.write(output_frame)
                
                frame_count += 1
                pbar.update(1)
                
            except Exception as e:
                print(f"Error processing frame {frame_count}: {str(e)}")
                continue
    
    # Cleanup
    cap.release()
    out.release()
    pbar.close()
    
    print(f"Processed {frame_count} frames")
    print(f"Output video saved to: {output_path}")
    
    return all_tracking_results


def process_image_sequence(model, image_dir, output_dir, device):
    """Process sequence of images."""
    # Get image files
    image_files = []
    for ext in ['*.jpg', '*.jpeg', '*.png']:
        image_files.extend(glob.glob(os.path.join(image_dir, ext)))
    
    image_files.sort()
    
    if len(image_files) == 0:
        raise ValueError(f"No images found in {image_dir}")
    
    print(f"Found {len(image_files)} images")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Reset tracker
    model.reset_tracker()
    
    # Process images
    all_tracking_results = []
    
    with torch.no_grad():
        for i, image_path in enumerate(tqdm(image_files, desc="Processing images")):
            try:
                # Load image
                image = cv2.imread(image_path)
                
                if image is None:
                    print(f"Cannot load image: {image_path}")
                    continue
                
                # Preprocess
                input_tensor, processed_image = preprocess_image(image)
                input_tensor = input_tensor.to(device)
                
                # Run inference (simplified demo)
                dummy_tracking_results = []
                
                # Draw results
                output_image = draw_tracking_results(
                    image, dummy_tracking_results,
                    show_ids=True, show_uncertainty=True
                )
                
                # Save output
                output_path = os.path.join(output_dir, f"output_{i:06d}.jpg")
                cv2.imwrite(output_path, output_image)
                
                all_tracking_results.append(dummy_tracking_results)
                
            except Exception as e:
                print(f"Error processing {image_path}: {str(e)}")
                continue
    
    print(f"Results saved to: {output_dir}")
    return all_tracking_results


def main():
    parser = argparse.ArgumentParser(description='MPPET-RLE Demo')
    parser.add_argument('--checkpoint', type=str, default=None,
                       help='Path to model checkpoint')
    parser.add_argument('--input', type=str, required=True,
                       help='Input video file or image directory')
    parser.add_argument('--output', type=str, required=True,
                       help='Output video file or directory')
    parser.add_argument('--device', type=str, default='cuda',
                       help='Device to use (cuda/cpu)')
    parser.add_argument('--max_frames', type=int, default=None,
                       help='Maximum number of frames to process')
    parser.add_argument('--save_results', type=str, default=None,
                       help='Save tracking results to JSON file')
    
    args = parser.parse_args()
    
    # Device
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    print(f'Using device: {device}')
    
    # Load model
    if args.checkpoint:
        model = load_model(args.checkpoint, device)
    else:
        print("No checkpoint provided, using randomly initialized model")
        model = MPPET_RLE().to(device)
        model.eval()
    
    # Process input
    if os.path.isfile(args.input):
        # Video file
        print(f"Processing video: {args.input}")
        tracking_results = process_video(
            model, args.input, args.output, device, args.max_frames
        )
    elif os.path.isdir(args.input):
        # Image directory
        print(f"Processing image sequence: {args.input}")
        tracking_results = process_image_sequence(
            model, args.input, args.output, device
        )
    else:
        raise ValueError(f"Input {args.input} is neither a file nor directory")
    
    # Save results
    if args.save_results:
        print(f"Saving tracking results to: {args.save_results}")
        
        # Convert numpy arrays to lists for JSON serialization
        def convert_numpy(obj):
            if isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, dict):
                return {key: convert_numpy(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy(item) for item in obj]
            else:
                return obj
        
        results_serializable = convert_numpy(tracking_results)
        
        with open(args.save_results, 'w') as f:
            json.dump(results_serializable, f, indent=2)
    
    print("Demo completed!")


if __name__ == '__main__':
    import glob
    main()
