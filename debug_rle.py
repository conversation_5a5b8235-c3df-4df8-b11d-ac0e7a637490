"""
Debug script for RLE pose estimation
"""

import torch
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_rle():
    """Debug RLE pose estimation step by step."""
    print("Debugging RLE Pose Estimation...")
    
    try:
        from RLE_pose import RegressFlow
        
        # Create simplified config
        config = {
            'NUM_LAYERS': 50,
            'NUM_FC_FILTERS': [1024],  # Single FC layer
            'HIDDEN_LIST': [256, 256],
            'PRESET': {
                'NUM_JOINTS': 17,
                'IMAGE_SIZE': [256, 192]
            }
        }
        
        print(f"Config: {config}")
        
        # Create model
        model = RegressFlow(**config)
        model.eval()
        
        print(f"Model created successfully")
        print(f"Feature channel: {model.feature_channel}")
        print(f"FC dims: {model.fc_dim}")
        
        # Check FC layers
        print(f"FC layers output channel: {model.fcs}")
        
        # Test with small input first
        batch_size = 1
        input_tensor = torch.randn(batch_size, 3, 256, 192)
        
        print(f"Input shape: {input_tensor.shape}")
        
        # Forward pass step by step
        with torch.no_grad():
            # Backbone
            feat = model.preact(input_tensor)
            print(f"After backbone: {feat.shape}")
            
            # Average pooling
            feat_pooled = model.avg_pool(feat).reshape(batch_size, -1)
            print(f"After pooling: {feat_pooled.shape}")
            
            # FC layers
            feat_fc = model.fcs(feat_pooled)
            print(f"After FC layers: {feat_fc.shape}")
            
            # Coordinate prediction
            try:
                out_coord = model.fc_coord(feat_fc)
                print(f"Coordinate output: {out_coord.shape}")
                
                out_sigma = model.fc_sigma(feat_fc)
                print(f"Sigma output: {out_sigma.shape}")
                
                print("✓ RLE forward pass successful!")
                return True
                
            except Exception as e:
                print(f"Error in final layers: {e}")
                print(f"FC output shape: {feat_fc.shape}")
                print(f"Expected input for fc_coord: {model.fc_coord.linear.in_features}")
                print(f"Expected output for fc_coord: {model.fc_coord.linear.out_features}")
                return False
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    debug_rle()
