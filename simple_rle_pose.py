"""
Improved RLE pose estimation with proper uncertainty modeling
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
from easydict import EasyDict
import torchvision.models as models

from builder import SPPE


@SPPE.register_module
class SimpleRegressFlow(nn.Module):
    """
    Improved RLE pose estimation model with proper uncertainty quantification.

    This model estimates both keypoint positions (μ) and uncertainties (σ) using
    a residual log-likelihood estimation approach.
    """

    def __init__(self, num_joints=17, input_size=(192, 256), normalize_coords=True,
                 backbone='resnet50', pretrained=True, **kwargs):
        super(SimpleRegressFlow, self).__init__()

        self.num_joints = num_joints
        self.input_size = input_size  # (width, height)
        self.normalize_coords = normalize_coords

        # Build backbone
        self.backbone = self._build_backbone(backbone, pretrained)

        # Feature dimensions (depends on backbone)
        if 'resnet50' in backbone:
            self.feature_dim = 2048
        elif 'resnet34' in backbone or 'resnet18' in backbone:
            self.feature_dim = 512
        else:
            self.feature_dim = 2048

        # Global average pooling
        self.avgpool = nn.AdaptiveAvgPool2d((1, 1))

        # Feature processing layers
        self.fc1 = nn.Linear(self.feature_dim, 1024)
        self.dropout1 = nn.Dropout(0.2)
        self.fc2 = nn.Linear(1024, 512)
        self.dropout2 = nn.Dropout(0.2)

        # Output heads for coordinates and uncertainties
        self.fc_coord = nn.Linear(512, num_joints * 2)
        self.fc_sigma = nn.Linear(512, num_joints * 2)

        # Additional layers for better uncertainty estimation
        self.fc_sigma_hidden = nn.Linear(512, 256)
        self.fc_sigma_out = nn.Linear(256, num_joints * 2)

        # Initialize weights
        self._initialize_weights()

        # Constants for RLE
        self.log_2pi = math.log(2 * math.pi)
        self.eps = 1e-6
    
    def _build_backbone(self, backbone_name, pretrained):
        """Build backbone network"""
        if backbone_name == 'resnet50':
            resnet = models.resnet50(pretrained=pretrained)
            backbone = nn.Sequential(*list(resnet.children())[:-2])
        elif backbone_name == 'resnet34':
            resnet = models.resnet34(pretrained=pretrained)
            backbone = nn.Sequential(*list(resnet.children())[:-2])
        elif backbone_name == 'resnet18':
            resnet = models.resnet18(pretrained=pretrained)
            backbone = nn.Sequential(*list(resnet.children())[:-2])
        else:
            raise ValueError(f"Unsupported backbone: {backbone_name}")

        return backbone

    def _initialize_weights(self):
        """Initialize network weights with proper initialization"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                if 'sigma' in str(m):
                    # Special initialization for uncertainty heads
                    nn.init.xavier_uniform_(m.weight, gain=0.01)
                    if m.bias is not None:
                        nn.init.constant_(m.bias, -2.0)  # Start with low uncertainty
                else:
                    nn.init.xavier_uniform_(m.weight, gain=1.0)
                    if m.bias is not None:
                        nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x, labels=None):
        """
        Forward pass with improved uncertainty estimation

        Args:
            x: Input images (B, C, H, W)
            labels: Ground truth labels (optional, for training)

        Returns:
            EasyDict with predictions and uncertainties
        """
        batch_size = x.shape[0]

        # Backbone feature extraction
        feat = self.backbone(x)

        # Global average pooling
        feat = self.avgpool(feat)
        feat = feat.view(batch_size, -1)

        # Feature processing with dropout
        feat = F.relu(self.fc1(feat))
        feat = self.dropout1(feat)
        feat = F.relu(self.fc2(feat))
        feat = self.dropout2(feat)

        # Coordinate prediction
        coord_out = self.fc_coord(feat).view(batch_size, self.num_joints, 2)

        # Uncertainty prediction with dedicated pathway
        sigma_feat = F.relu(self.fc_sigma_hidden(feat))
        sigma_out = self.fc_sigma_out(sigma_feat).view(batch_size, self.num_joints, 2)

        # Apply proper activation for uncertainty (ensure positive values)
        sigma = F.softplus(sigma_out) + self.eps

        # Normalize coordinates if requested
        if self.normalize_coords:
            # Coordinates are already normalized in dataset, apply sigmoid to keep in [0,1]
            coord_out = torch.sigmoid(coord_out)

        # Compute confidence scores (inverse of uncertainty)
        confidence = 1.0 / (1.0 + sigma.mean(dim=2, keepdim=True))

        # Compute normalizing flow loss for training
        nf_loss = None
        if self.training and labels is not None:
            nf_loss = self._compute_nf_loss(coord_out, sigma, labels)

        output = EasyDict(
            pred_jts=coord_out,
            sigma=sigma,
            maxvals=confidence.float(),
            nf_loss=nf_loss
        )

        return output

    def _compute_nf_loss(self, pred_coords, pred_sigma, labels):
        """
        Compute the normalizing flow loss for RLE

        Args:
            pred_coords: Predicted coordinates (B, N, 2)
            pred_sigma: Predicted uncertainties (B, N, 2)
            labels: Ground truth labels

        Returns:
            Normalizing flow loss
        """
        # Extract ground truth
        if isinstance(labels, dict):
            gt_coords = labels.get('target_uv', labels.get('poses'))
            gt_weights = labels.get('target_uv_weight', labels.get('pose_weights'))
        else:
            gt_coords = labels
            gt_weights = torch.ones_like(labels)

        if gt_coords is None:
            return torch.tensor(0.0, device=pred_coords.device, requires_grad=True)

        # Ensure same shape
        if gt_coords.dim() == 3 and gt_coords.shape != pred_coords.shape:
            gt_coords = gt_coords.view_as(pred_coords)
        if gt_weights.dim() == 3 and gt_weights.shape != pred_coords.shape:
            gt_weights = gt_weights.view_as(pred_coords)

        # Compute residual
        residual = pred_coords - gt_coords

        # RLE loss: log(σ) + |residual|²/(2σ²)
        # This is the negative log-likelihood of a Laplace distribution
        nf_loss = torch.log(pred_sigma + self.eps) + (residual ** 2) / (2 * pred_sigma ** 2 + self.eps)

        # Apply weights
        if gt_weights is not None:
            nf_loss = nf_loss * gt_weights

        # Average over valid keypoints
        if gt_weights is not None and gt_weights.sum() > 0:
            return nf_loss.sum() / gt_weights.sum()
        else:
            return nf_loss.mean()


def build_simple_rle_pose(num_joints=17, input_size=(192, 256), normalize_coords=True,
                         backbone='resnet50', pretrained=True):
    """Build improved RLE pose model with proper configuration."""
    return SimpleRegressFlow(
        num_joints=num_joints,
        input_size=input_size,
        normalize_coords=normalize_coords,
        backbone=backbone,
        pretrained=pretrained
    )
