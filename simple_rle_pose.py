"""
Simplified RLE pose estimation for training
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from easydict import EasyDict
import torchvision.models as models

from builder import SPPE


@SPPE.register_module
class SimpleRegressFlow(nn.Module):
    """Simplified RLE pose estimation model."""
    
    def __init__(self, num_joints=17, **kwargs):
        super(SimpleRegressFlow, self).__init__()
        
        self.num_joints = num_joints
        
        # Use ResNet50 backbone
        resnet = models.resnet50(pretrained=True)
        self.backbone = nn.Sequential(*list(resnet.children())[:-2])  # Remove avgpool and fc
        
        # Feature dimensions
        self.feature_dim = 2048
        
        # Average pooling
        self.avgpool = nn.AdaptiveAvgPool2d((1, 1))
        
        # FC layers
        self.fc1 = nn.Linear(self.feature_dim, 1024)
        self.fc2 = nn.Linear(1024, 512)
        
        # Output heads
        self.fc_coord = nn.Linear(512, num_joints * 2)
        self.fc_sigma = nn.Linear(512, num_joints * 2)
        
        # Initialize weights
        self._initialize_weights()
    
    def _initialize_weights(self):
        """Initialize weights."""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight, gain=0.01)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
    
    def forward(self, x, labels=None):
        batch_size = x.shape[0]
        
        # Backbone
        feat = self.backbone(x)
        
        # Global average pooling
        feat = self.avgpool(feat)
        feat = feat.view(batch_size, -1)
        
        # FC layers
        feat = F.relu(self.fc1(feat))
        feat = F.relu(self.fc2(feat))
        
        # Output heads
        coord_out = self.fc_coord(feat).view(batch_size, self.num_joints, 2)
        sigma_out = self.fc_sigma(feat).view(batch_size, self.num_joints, 2)
        
        # Apply sigmoid to sigma for positive values
        sigma = torch.sigmoid(sigma_out) + 1e-6  # Add small epsilon
        
        # Confidence scores (1 - sigma)
        scores = 1.0 - torch.mean(sigma, dim=2, keepdim=True)
        
        # Simplified NF loss (placeholder)
        if self.training and labels is not None:
            gt_uv = labels['target_uv'].view(batch_size, self.num_joints, 2)
            
            # Simple L2 loss weighted by uncertainty
            diff = coord_out - gt_uv
            nf_loss = torch.log(sigma) + (diff ** 2) / (2 * sigma ** 2)
        else:
            nf_loss = None
        
        output = EasyDict(
            pred_jts=coord_out,
            sigma=sigma,
            maxvals=scores.float(),
            nf_loss=nf_loss
        )
        
        return output


def build_simple_rle_pose(num_joints=17):
    """Build simplified RLE pose model."""
    return SimpleRegressFlow(num_joints=num_joints)
