"""
Multi-person detection module for MPPET-RLE
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.ops as ops
from torchvision.models.detection import fasterrcnn_resnet50_fpn
from torchvision.models.detection.faster_rcnn import FastRCNNPredictor

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from builder import DETECTOR


@DETECTOR.register_module
class FasterRCNNDetector(nn.Module):
    """Faster R-CNN based person detector."""
    
    def __init__(self, pretrained=True, num_classes=2, score_threshold=0.5, nms_threshold=0.5):
        """
        Initialize Faster R-CNN detector.
        
        Args:
            pretrained: Whether to use pretrained weights
            num_classes: Number of classes (background + person)
            score_threshold: Score threshold for filtering detections
            nms_threshold: NMS threshold for filtering detections
        """
        super(FasterRCNNDetector, self).__init__()
        
        self.score_threshold = score_threshold
        self.nms_threshold = nms_threshold
        
        # Load pretrained Faster R-CNN
        self.model = fasterrcnn_resnet50_fpn(pretrained=pretrained)
        
        # Replace classifier head for person detection (background + person)
        in_features = self.model.roi_heads.box_predictor.cls_score.in_features
        self.model.roi_heads.box_predictor = FastRCNNPredictor(in_features, num_classes)
        
    def forward(self, images, targets=None):
        """
        Forward pass of the detector.
        
        Args:
            images: List of images or tensor of shape (B, C, H, W)
            targets: List of target dictionaries for training
            
        Returns:
            If training: losses dict
            If inference: list of detection results
        """
        if self.training and targets is not None:
            # Training mode - return losses
            return self.model(images, targets)
        else:
            # Inference mode - return detections
            self.model.eval()
            with torch.no_grad():
                predictions = self.model(images)
            
            # Filter predictions
            filtered_predictions = []
            for pred in predictions:
                # Filter by score threshold
                keep = pred['scores'] > self.score_threshold
                
                # Filter person class only (class 1)
                person_mask = pred['labels'] == 1
                keep = keep & person_mask
                
                if keep.sum() > 0:
                    filtered_pred = {
                        'boxes': pred['boxes'][keep],
                        'scores': pred['scores'][keep],
                        'labels': pred['labels'][keep]
                    }
                    
                    # Apply NMS
                    keep_nms = ops.nms(filtered_pred['boxes'], filtered_pred['scores'], self.nms_threshold)
                    
                    final_pred = {
                        'boxes': filtered_pred['boxes'][keep_nms],
                        'scores': filtered_pred['scores'][keep_nms],
                        'labels': filtered_pred['labels'][keep_nms]
                    }
                else:
                    final_pred = {
                        'boxes': torch.empty((0, 4), device=pred['boxes'].device),
                        'scores': torch.empty((0,), device=pred['scores'].device),
                        'labels': torch.empty((0,), device=pred['labels'].device, dtype=torch.long)
                    }
                
                filtered_predictions.append(final_pred)
            
            return filtered_predictions
    
    def extract_features(self, images):
        """Extract backbone features for pose estimation."""
        self.model.eval()
        with torch.no_grad():
            # Get backbone features
            features = self.model.backbone(images)
        return features


@DETECTOR.register_module  
class YOLODetector(nn.Module):
    """YOLO-based person detector (simplified implementation)."""
    
    def __init__(self, backbone='resnet50', num_classes=1, score_threshold=0.5, nms_threshold=0.5):
        """
        Initialize YOLO detector.
        
        Args:
            backbone: Backbone network
            num_classes: Number of classes (person only)
            score_threshold: Score threshold for filtering detections
            nms_threshold: NMS threshold for filtering detections
        """
        super(YOLODetector, self).__init__()
        
        self.score_threshold = score_threshold
        self.nms_threshold = nms_threshold
        self.num_classes = num_classes
        
        # Simplified YOLO head - in practice, use a proper YOLO implementation
        if backbone == 'resnet50':
            import torchvision.models as models
            self.backbone = models.resnet50(pretrained=True)
            self.backbone = nn.Sequential(*list(self.backbone.children())[:-2])  # Remove avgpool and fc
            feature_dim = 2048
        else:
            raise NotImplementedError(f"Backbone {backbone} not implemented")
        
        # YOLO detection head
        self.detection_head = nn.Sequential(
            nn.Conv2d(feature_dim, 512, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(512, 256, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, (5 + num_classes) * 3, 1)  # 3 anchors, 5 = (x, y, w, h, conf)
        )
        
    def forward(self, images, targets=None):
        """Forward pass of YOLO detector."""
        batch_size = images.shape[0]
        
        # Extract features
        features = self.backbone(images)
        
        # Detection head
        detections = self.detection_head(features)
        
        if self.training and targets is not None:
            # Training mode - compute losses
            # This is a simplified implementation
            # In practice, implement proper YOLO loss
            return {'loss_detection': torch.tensor(0.0, device=images.device, requires_grad=True)}
        else:
            # Inference mode - decode predictions
            # This is a simplified implementation
            # In practice, implement proper YOLO decoding
            predictions = []
            for i in range(batch_size):
                pred = {
                    'boxes': torch.empty((0, 4), device=images.device),
                    'scores': torch.empty((0,), device=images.device),
                    'labels': torch.empty((0,), device=images.device, dtype=torch.long)
                }
                predictions.append(pred)
            
            return predictions
    
    def extract_features(self, images):
        """Extract backbone features for pose estimation."""
        return self.backbone(images)


def build_detector(cfg):
    """Build detector from config."""
    detector_type = cfg.get('type', 'FasterRCNNDetector')
    
    if detector_type == 'FasterRCNNDetector':
        return FasterRCNNDetector(
            pretrained=cfg.get('pretrained', True),
            num_classes=cfg.get('num_classes', 2),
            score_threshold=cfg.get('score_threshold', 0.5),
            nms_threshold=cfg.get('nms_threshold', 0.5)
        )
    elif detector_type == 'YOLODetector':
        return YOLODetector(
            backbone=cfg.get('backbone', 'resnet50'),
            num_classes=cfg.get('num_classes', 1),
            score_threshold=cfg.get('score_threshold', 0.5),
            nms_threshold=cfg.get('nms_threshold', 0.5)
        )
    else:
        raise ValueError(f"Unknown detector type: {detector_type}")
