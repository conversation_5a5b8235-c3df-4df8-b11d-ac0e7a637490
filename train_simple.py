"""
Simplified training script for MPPET-RLE
"""

import os
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import argparse
from tqdm import tqdm
import logging
from datetime import datetime

# Import our modules
from simple_rle_pose import SimpleRegress<PERSON>low
from RLE_regression_loss import <PERSON><PERSON><PERSON><PERSON>
from utils.visualization import draw_pose_with_uncertainty
import cv2


def setup_logging(log_dir):
    """Setup logging configuration."""
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, f'training_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    return logging.getLogger(__name__)


def create_dummy_dataset(num_samples=100, batch_size=4):
    """Create dummy dataset for testing training pipeline."""
    
    class DummyDataset:
        def __init__(self, num_samples):
            self.num_samples = num_samples
            
        def __len__(self):
            return self.num_samples
        
        def __getitem__(self, idx):
            # Create dummy image (person crop)
            image = torch.randn(3, 256, 192)
            
            # Create dummy pose (17 joints, 2D coordinates)
            pose = torch.randn(17, 2) * 50 + 128  # Center around image center
            
            # Create dummy weights (all visible)
            weights = torch.ones(17, 2)
            
            return {
                'image': image,
                'target_uv': pose,
                'target_uv_weight': weights
            }
    
    dataset = DummyDataset(num_samples)
    
    def collate_fn(batch):
        images = torch.stack([item['image'] for item in batch])
        target_uv = torch.stack([item['target_uv'] for item in batch])
        target_uv_weight = torch.stack([item['target_uv_weight'] for item in batch])
        
        return {
            'images': images,
            'targets': {
                'target_uv': target_uv,
                'target_uv_weight': target_uv_weight
            }
        }
    
    dataloader = torch.utils.data.DataLoader(
        dataset, 
        batch_size=batch_size, 
        shuffle=True, 
        collate_fn=collate_fn
    )
    
    return dataloader


def train_epoch(model, dataloader, optimizer, criterion, device, logger, epoch):
    """Train for one epoch."""
    model.train()
    
    total_loss = 0.0
    num_batches = 0
    
    pbar = tqdm(dataloader, desc=f'Epoch {epoch}')
    
    for batch_idx, batch in enumerate(pbar):
        images = batch['images'].to(device)
        targets = {
            'target_uv': batch['targets']['target_uv'].to(device),
            'target_uv_weight': batch['targets']['target_uv_weight'].to(device)
        }
        
        optimizer.zero_grad()
        
        # Forward pass
        outputs = model(images, targets)
        
        # Compute loss
        loss = criterion(outputs, targets)
        
        # Backward pass
        loss.backward()
        
        # Gradient clipping
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        
        optimizer.step()
        
        total_loss += loss.item()
        num_batches += 1
        
        # Update progress bar
        pbar.set_postfix({'Loss': f'{loss.item():.4f}'})
    
    avg_loss = total_loss / max(num_batches, 1)
    logger.info(f'Epoch {epoch} - Avg Loss: {avg_loss:.4f}')
    
    return avg_loss


def validate_epoch(model, dataloader, device, logger, epoch, vis_dir=None):
    """Validate for one epoch."""
    model.eval()
    
    total_loss = 0.0
    num_batches = 0
    
    with torch.no_grad():
        pbar = tqdm(dataloader, desc=f'Validation Epoch {epoch}')
        
        for batch_idx, batch in enumerate(pbar):
            images = batch['images'].to(device)
            targets = {
                'target_uv': batch['targets']['target_uv'].to(device),
                'target_uv_weight': batch['targets']['target_uv_weight'].to(device)
            }
            
            # Forward pass
            outputs = model(images)
            
            # For visualization (save a few samples)
            if vis_dir and batch_idx == 0:
                for i in range(min(len(images), 3)):
                    img_np = images[i].cpu().permute(1, 2, 0).numpy()
                    img_np = ((img_np + 1) * 127.5).clip(0, 255).astype(np.uint8)
                    
                    # Get predictions
                    pred_pose = outputs.pred_jts[i].cpu().numpy()
                    pred_sigma = outputs.sigma[i].cpu().numpy()
                    gt_pose = targets['target_uv'][i].cpu().numpy()
                    
                    # Draw poses
                    vis_img = draw_pose_with_uncertainty(img_np, pred_pose, pred_sigma, color=(0, 255, 0))
                    
                    # Draw ground truth
                    for j, point in enumerate(gt_pose):
                        x, y = int(point[0]), int(point[1])
                        if 0 <= x < img_np.shape[1] and 0 <= y < img_np.shape[0]:
                            cv2.circle(vis_img, (x, y), 3, (255, 0, 0), -1)  # Blue for GT
                    
                    # Save visualization
                    save_path = os.path.join(vis_dir, f'val_epoch_{epoch}_img_{i}.jpg')
                    cv2.imwrite(save_path, cv2.cvtColor(vis_img, cv2.COLOR_RGB2BGR))
            
            num_batches += 1
    
    logger.info(f'Validation Epoch {epoch} - Processed {num_batches} batches')
    
    return {'num_batches': num_batches}


def main():
    parser = argparse.ArgumentParser(description='Train MPPET-RLE (Simplified)')
    parser.add_argument('--batch_size', type=int, default=8, help='Batch size')
    parser.add_argument('--epochs', type=int, default=20, help='Number of epochs')
    parser.add_argument('--lr', type=float, default=1e-4, help='Learning rate')
    parser.add_argument('--weight_decay', type=float, default=1e-4, help='Weight decay')
    parser.add_argument('--save_dir', type=str, default='checkpoints', help='Save directory')
    parser.add_argument('--log_dir', type=str, default='logs', help='Log directory')
    parser.add_argument('--vis_dir', type=str, default='visualizations', help='Visualization directory')
    parser.add_argument('--device', type=str, default='cuda', help='Device to use')
    parser.add_argument('--num_samples', type=int, default=200, help='Number of dummy samples')
    
    args = parser.parse_args()
    
    # Setup directories
    os.makedirs(args.save_dir, exist_ok=True)
    os.makedirs(args.vis_dir, exist_ok=True)
    
    # Setup logging
    logger = setup_logging(args.log_dir)
    logger.info(f'Starting training with args: {args}')
    
    # Device
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    logger.info(f'Using device: {device}')
    
    # Create dummy dataset
    train_loader = create_dummy_dataset(args.num_samples, args.batch_size)
    val_loader = create_dummy_dataset(args.num_samples // 4, args.batch_size)
    
    logger.info(f'Created dummy dataset with {args.num_samples} training samples')
    
    # Model
    model = SimpleRegressFlow(num_joints=17)
    model = model.to(device)
    
    logger.info(f'Model created with {sum(p.numel() for p in model.parameters())} parameters')
    
    # Loss function
    criterion = RLELoss()
    
    # Optimizer
    optimizer = optim.AdamW(model.parameters(), lr=args.lr, weight_decay=args.weight_decay)
    
    # Learning rate scheduler
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=args.epochs)
    
    # Training loop
    best_loss = float('inf')
    
    for epoch in range(args.epochs):
        logger.info(f'Starting epoch {epoch}/{args.epochs}')
        
        # Train
        train_loss = train_epoch(model, train_loader, optimizer, criterion, device, logger, epoch)
        
        # Validate
        val_metrics = validate_epoch(
            model, val_loader, device, logger, epoch, 
            vis_dir=args.vis_dir if epoch % 5 == 0 else None
        )
        
        # Update learning rate
        scheduler.step()
        
        # Save checkpoint
        if train_loss < best_loss:
            best_loss = train_loss
            checkpoint = {
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'train_loss': train_loss,
                'best_loss': best_loss
            }
            
            save_path = os.path.join(args.save_dir, 'best_model.pth')
            torch.save(checkpoint, save_path)
            logger.info(f'Saved best model with loss {best_loss:.4f}')
        
        # Save regular checkpoint
        if epoch % 5 == 0:
            checkpoint = {
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'train_loss': train_loss,
                'best_loss': best_loss
            }
            
            save_path = os.path.join(args.save_dir, f'checkpoint_epoch_{epoch}.pth')
            torch.save(checkpoint, save_path)
            logger.info(f'Saved checkpoint for epoch {epoch}')
    
    logger.info('Training completed!')
    logger.info(f'Best loss: {best_loss:.4f}')


if __name__ == '__main__':
    main()
