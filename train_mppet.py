"""
Training script for MPPET-RLE
"""

import os
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import argparse
from tqdm import tqdm
import logging
from datetime import datetime

from mppet_rle import MPPET_RLE
from RLE_regression_loss import R<PERSON><PERSON><PERSON>, AdaptiveRLELoss
from datasets.posetrack_dataset import PoseTrackDataset, get_transform, collate_fn
from utils.visualization import draw_tracking_results, visualize_pose_comparison
from utils.metrics import PoseTrackingMetrics
import cv2


def setup_logging(log_dir):
    """Setup logging configuration."""
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, f'training_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    return logging.getLogger(__name__)


# Remove the old collate_fn as it's now in the dataset module


def train_epoch(model, dataloader, optimizer, criterion, device, logger, epoch):
    """Train for one epoch with improved data handling."""
    model.train()

    total_loss = 0.0
    total_pose_loss = 0.0
    num_batches = 0
    num_samples = 0

    pbar = tqdm(dataloader, desc=f'Epoch {epoch}')

    for batch_idx, batch_data in enumerate(pbar):
        try:
            # Extract data from new collate format
            images = batch_data['images']
            poses = batch_data['poses']
            pose_weights = batch_data['pose_weights']

            if len(images) == 0:
                continue

            # Move to device
            images = images.to(device)
            poses = poses.to(device)
            pose_weights = pose_weights.to(device)

            # Prepare labels for loss computation
            labels = {
                'poses': poses,
                'pose_weights': pose_weights
            }

            optimizer.zero_grad()

            # Forward pass through pose estimator
            if hasattr(model, 'pose_estimator'):
                outputs = model.pose_estimator(images, labels)
            else:
                # Direct model call for simplified training
                outputs = model(images, labels, mode='train')

            # Compute loss
            pose_loss = criterion(outputs, labels)
            loss = pose_loss

            # Backward pass
            loss.backward()

            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)

            optimizer.step()

            # Update statistics
            total_loss += loss.item()
            total_pose_loss += pose_loss.item()
            num_batches += 1
            num_samples += len(images)

            # Update progress bar
            pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Samples': num_samples
            })

        except Exception as e:
            logger.error(f"Error in batch {batch_idx}: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            continue

    avg_loss = total_loss / max(num_batches, 1)
    avg_pose_loss = total_pose_loss / max(num_batches, 1)

    logger.info(f'Epoch {epoch} - Avg Loss: {avg_loss:.4f}, Samples: {num_samples}')

    return avg_loss, avg_pose_loss


def validate_epoch(model, dataloader, device, logger, epoch, vis_dir=None):
    """Validate for one epoch with improved visualization."""
    model.eval()

    total_samples = 0
    total_val_loss = 0.0
    num_batches = 0

    # Create validation criterion
    val_criterion = RLELoss(use_target_weight=True, size_average=True)

    with torch.no_grad():
        pbar = tqdm(dataloader, desc=f'Validation Epoch {epoch}')

        for batch_idx, batch_data in enumerate(pbar):
            try:
                # Extract data
                images = batch_data['images']
                poses = batch_data['poses']
                pose_weights = batch_data['pose_weights']

                if len(images) == 0:
                    continue

                # Move to device
                images = images.to(device)
                poses = poses.to(device)
                pose_weights = pose_weights.to(device)

                # Prepare labels
                labels = {
                    'poses': poses,
                    'pose_weights': pose_weights
                }

                # Forward pass
                if hasattr(model, 'pose_estimator'):
                    outputs = model.pose_estimator(images)
                else:
                    outputs = model(images, mode='detect')

                # Compute validation loss
                val_loss = val_criterion(outputs, labels)
                total_val_loss += val_loss.item()
                num_batches += 1

                # Extract predictions for visualization
                pred_poses = outputs.pred_jts.cpu().numpy()
                pred_sigmas = outputs.sigma.cpu().numpy()
                gt_poses = poses.cpu().numpy()

                # Denormalize coordinates for visualization if needed
                if hasattr(model, 'pose_estimator') and model.pose_estimator.normalize_coords:
                    # Convert from [0,1] back to pixel coordinates
                    input_size = model.pose_estimator.input_size
                    pred_poses[..., 0] *= input_size[0]  # width
                    pred_poses[..., 1] *= input_size[1]  # height
                    gt_poses[..., 0] *= input_size[0]
                    gt_poses[..., 1] *= input_size[1]

                # Visualization (save a few samples)
                if vis_dir and batch_idx < 3:
                    os.makedirs(vis_dir, exist_ok=True)
                    for i in range(min(len(images), 3)):
                        # Denormalize image
                        img_tensor = images[i].cpu()
                        img_np = img_tensor.permute(1, 2, 0).numpy()

                        # Reverse ImageNet normalization
                        mean = np.array([0.485, 0.456, 0.406])
                        std = np.array([0.229, 0.224, 0.225])
                        img_np = img_np * std + mean
                        img_np = np.clip(img_np * 255, 0, 255).astype(np.uint8)

                        # Create comparison visualization
                        if i < len(pred_poses):
                            vis_img = visualize_pose_comparison(
                                img_np, gt_poses[i], pred_poses[i],
                                pred_sigmas[i] if i < len(pred_sigmas) else None
                            )

                            # Save visualization
                            save_path = os.path.join(vis_dir, f'val_epoch_{epoch}_img_{i}.jpg')
                            cv2.imwrite(save_path, cv2.cvtColor(vis_img, cv2.COLOR_RGB2BGR))

                total_samples += len(images)

                # Update progress bar
                pbar.set_postfix({
                    'Val Loss': f'{val_loss.item():.4f}',
                    'Samples': total_samples
                })

            except Exception as e:
                logger.error(f"Error in validation batch {batch_idx}: {str(e)}")
                import traceback
                logger.error(traceback.format_exc())
                continue

    avg_val_loss = total_val_loss / max(num_batches, 1)
    logger.info(f'Validation Epoch {epoch} - Avg Loss: {avg_val_loss:.4f}, Samples: {total_samples}')

    return {'total_samples': total_samples, 'avg_loss': avg_val_loss}


def main():
    parser = argparse.ArgumentParser(description='Train MPPET-RLE')
    parser.add_argument('--data_root', type=str, default='datasets/PoseTrack21/data',
                       help='Path to PoseTrack21 dataset')
    parser.add_argument('--batch_size', type=int, default=8, help='Batch size')
    parser.add_argument('--epochs', type=int, default=100, help='Number of epochs')
    parser.add_argument('--lr', type=float, default=1e-4, help='Learning rate')
    parser.add_argument('--weight_decay', type=float, default=1e-4, help='Weight decay')
    parser.add_argument('--save_dir', type=str, default='checkpoints', help='Save directory')
    parser.add_argument('--log_dir', type=str, default='logs', help='Log directory')
    parser.add_argument('--vis_dir', type=str, default='visualizations', help='Visualization directory')
    parser.add_argument('--resume', type=str, default=None, help='Resume from checkpoint')
    parser.add_argument('--device', type=str, default='cuda', help='Device to use')
    
    args = parser.parse_args()
    
    # Setup directories
    os.makedirs(args.save_dir, exist_ok=True)
    os.makedirs(args.vis_dir, exist_ok=True)
    
    # Setup logging
    logger = setup_logging(args.log_dir)
    logger.info(f'Starting training with args: {args}')
    
    # Device
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    logger.info(f'Using device: {device}')
    
    # Dataset and DataLoader with improved configuration
    input_size = (192, 256)  # (width, height)

    train_transform = get_transform('train', input_size)
    val_transform = get_transform('val', input_size)

    train_dataset = PoseTrackDataset(
        data_root=args.data_root,
        split='train',
        transform=train_transform,
        sequence_length=1,  # Focus on single frame pose estimation
        input_size=input_size,
        normalize_coords=True,
        use_gt_bbox=True
    )

    val_dataset = PoseTrackDataset(
        data_root=args.data_root,
        split='val',
        transform=val_transform,
        sequence_length=1,
        input_size=input_size,
        normalize_coords=True,
        use_gt_bbox=True
    )

    train_loader = DataLoader(
        train_dataset,
        batch_size=args.batch_size,
        shuffle=True,
        num_workers=2,  # Reduced for stability
        collate_fn=collate_fn,
        pin_memory=True,
        drop_last=True  # Ensure consistent batch sizes
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=2,
        collate_fn=collate_fn,
        pin_memory=True,
        drop_last=False
    )
    
    logger.info(f'Train dataset: {len(train_dataset)} samples')
    logger.info(f'Val dataset: {len(val_dataset)} samples')
    
    # Model with improved configuration
    pose_config = {
        'num_joints': 17,
        'input_size': input_size,
        'normalize_coords': True,
        'backbone': 'resnet50',
        'pretrained': True
    }

    model = MPPET_RLE(pose_config=pose_config)
    model = model.to(device)

    # Loss function with adaptive weighting
    criterion = AdaptiveRLELoss(
        use_target_weight=True,
        loss_weight=1.0,
        residual_weight=1.0,
        nf_weight=0.5,  # Lower weight for NF loss initially
        size_average=True
    )
    
    # Optimizer
    optimizer = optim.AdamW(model.parameters(), lr=args.lr, weight_decay=args.weight_decay)
    
    # Learning rate scheduler
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=args.epochs)
    
    # Resume from checkpoint
    start_epoch = 0
    if args.resume:
        if os.path.isfile(args.resume):
            logger.info(f'Loading checkpoint {args.resume}')
            checkpoint = torch.load(args.resume, map_location=device)
            model.load_state_dict(checkpoint['model_state_dict'])
            optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            start_epoch = checkpoint['epoch'] + 1
            logger.info(f'Resumed from epoch {start_epoch}')
        else:
            logger.warning(f'Checkpoint {args.resume} not found')
    
    # Training loop
    best_loss = float('inf')
    
    for epoch in range(start_epoch, args.epochs):
        logger.info(f'Starting epoch {epoch}/{args.epochs}')
        
        # Train
        train_loss, train_pose_loss = train_epoch(
            model, train_loader, optimizer, criterion, device, logger, epoch
        )
        
        # Validate
        val_metrics = validate_epoch(
            model, val_loader, device, logger, epoch, 
            vis_dir=args.vis_dir if epoch % 10 == 0 else None
        )
        
        # Update learning rate
        scheduler.step()
        
        # Save checkpoint
        if train_loss < best_loss:
            best_loss = train_loss
            checkpoint = {
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'train_loss': train_loss,
                'best_loss': best_loss
            }
            
            save_path = os.path.join(args.save_dir, 'best_model.pth')
            torch.save(checkpoint, save_path)
            logger.info(f'Saved best model with loss {best_loss:.4f}')
        
        # Save regular checkpoint
        if epoch % 10 == 0:
            checkpoint = {
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'train_loss': train_loss,
                'best_loss': best_loss
            }
            
            save_path = os.path.join(args.save_dir, f'checkpoint_epoch_{epoch}.pth')
            torch.save(checkpoint, save_path)
            logger.info(f'Saved checkpoint for epoch {epoch}')
    
    logger.info('Training completed!')


if __name__ == '__main__':
    main()
