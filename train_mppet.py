"""
Training script for MPPET-RLE
"""

import os
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import argparse
from tqdm import tqdm
import logging
from datetime import datetime

from mppet_rle import MPPET_RLE
from RLE_regression_loss import R<PERSON>Loss
from datasets.posetrack_dataset import PoseTrackDataset, get_transform
from utils.visualization import draw_tracking_results, visualize_pose_comparison
from utils.metrics import PoseTrackingMetrics
import cv2


def setup_logging(log_dir):
    """Setup logging configuration."""
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, f'training_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    return logging.getLogger(__name__)


def collate_fn(batch):
    """Custom collate function for DataLoader."""
    # This is a simplified collate function
    # In practice, you would need to handle variable number of persons per image
    
    images = []
    targets = {'detection_targets': [], 'pose_targets': []}
    
    for sample in batch:
        # Extract person crops as images
        person_crops = [crop for crop, _, _ in sample['images']]
        if len(person_crops) > 0:
            images.extend(person_crops)
            
            # Create pose targets
            pose_targets = []
            for crop, pose, bbox in sample['images']:
                target = {
                    'target_uv': torch.tensor(pose, dtype=torch.float32),
                    'target_uv_weight': torch.ones_like(torch.tensor(pose, dtype=torch.float32))
                }
                pose_targets.append(target)
            
            targets['pose_targets'].extend(pose_targets)
    
    if len(images) > 0:
        images = torch.stack(images)
    else:
        images = torch.empty(0, 3, 256, 192)
    
    return images, targets


def train_epoch(model, dataloader, optimizer, criterion, device, logger, epoch):
    """Train for one epoch."""
    model.train()
    
    total_loss = 0.0
    total_pose_loss = 0.0
    total_detection_loss = 0.0
    num_batches = 0
    
    pbar = tqdm(dataloader, desc=f'Epoch {epoch}')
    
    for batch_idx, (images, targets) in enumerate(pbar):
        if len(images) == 0:
            continue
            
        images = images.to(device)
        
        # Prepare targets for device
        pose_targets = {}
        if 'pose_targets' in targets and len(targets['pose_targets']) > 0:
            pose_targets['target_uv'] = torch.stack([
                t['target_uv'] for t in targets['pose_targets']
            ]).to(device)
            pose_targets['target_uv_weight'] = torch.stack([
                t['target_uv_weight'] for t in targets['pose_targets']
            ]).to(device)
        
        optimizer.zero_grad()
        
        # Forward pass
        try:
            # For training, we focus on pose estimation
            # Use the pose estimator directly on person crops
            if hasattr(model, 'pose_estimator'):
                outputs = model.pose_estimator(images, pose_targets if pose_targets else None)
            else:
                # Fallback if model structure is different
                outputs = model(images, targets, mode='train')
            
            # Compute pose loss
            if pose_targets:
                pose_loss = criterion(outputs, pose_targets)
                loss = pose_loss
                
                total_pose_loss += pose_loss.item()
            else:
                loss = torch.tensor(0.0, device=device, requires_grad=True)
            
            # Backward pass
            loss.backward()
            
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
            
            # Update progress bar
            pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Pose Loss': f'{pose_loss.item():.4f}' if pose_targets else '0.0000'
            })
            
        except Exception as e:
            logger.error(f"Error in batch {batch_idx}: {str(e)}")
            continue
    
    avg_loss = total_loss / max(num_batches, 1)
    avg_pose_loss = total_pose_loss / max(num_batches, 1)
    
    logger.info(f'Epoch {epoch} - Avg Loss: {avg_loss:.4f}, Avg Pose Loss: {avg_pose_loss:.4f}')
    
    return avg_loss, avg_pose_loss


def validate_epoch(model, dataloader, device, logger, epoch, vis_dir=None):
    """Validate for one epoch."""
    model.eval()
    
    metrics_calculator = PoseTrackingMetrics()
    total_samples = 0
    
    with torch.no_grad():
        pbar = tqdm(dataloader, desc=f'Validation Epoch {epoch}')
        
        for batch_idx, (images, targets) in enumerate(pbar):
            if len(images) == 0:
                continue
                
            images = images.to(device)
            
            try:
                # Forward pass
                outputs = model.pose_estimator(images)
                
                # Extract predictions
                pred_poses = outputs.pred_jts.cpu().numpy()
                pred_sigmas = outputs.sigma.cpu().numpy()
                
                # For visualization (save a few samples)
                if vis_dir and batch_idx < 5:
                    for i in range(min(len(images), 3)):
                        img_np = images[i].cpu().permute(1, 2, 0).numpy()
                        img_np = (img_np * 255).astype(np.uint8)
                        
                        # Draw predicted pose with uncertainty
                        vis_img = img_np.copy()
                        if i < len(pred_poses):
                            from utils.visualization import draw_pose_with_uncertainty
                            vis_img = draw_pose_with_uncertainty(
                                vis_img, pred_poses[i], pred_sigmas[i]
                            )
                        
                        # Save visualization
                        save_path = os.path.join(vis_dir, f'val_epoch_{epoch}_batch_{batch_idx}_img_{i}.jpg')
                        cv2.imwrite(save_path, cv2.cvtColor(vis_img, cv2.COLOR_RGB2BGR))
                
                total_samples += len(images)
                
            except Exception as e:
                logger.error(f"Error in validation batch {batch_idx}: {str(e)}")
                continue
    
    logger.info(f'Validation Epoch {epoch} - Processed {total_samples} samples')
    
    return {'total_samples': total_samples}


def main():
    parser = argparse.ArgumentParser(description='Train MPPET-RLE')
    parser.add_argument('--data_root', type=str, default='datasets/PoseTrack21/data',
                       help='Path to PoseTrack21 dataset')
    parser.add_argument('--batch_size', type=int, default=8, help='Batch size')
    parser.add_argument('--epochs', type=int, default=100, help='Number of epochs')
    parser.add_argument('--lr', type=float, default=1e-4, help='Learning rate')
    parser.add_argument('--weight_decay', type=float, default=1e-4, help='Weight decay')
    parser.add_argument('--save_dir', type=str, default='checkpoints', help='Save directory')
    parser.add_argument('--log_dir', type=str, default='logs', help='Log directory')
    parser.add_argument('--vis_dir', type=str, default='visualizations', help='Visualization directory')
    parser.add_argument('--resume', type=str, default=None, help='Resume from checkpoint')
    parser.add_argument('--device', type=str, default='cuda', help='Device to use')
    
    args = parser.parse_args()
    
    # Setup directories
    os.makedirs(args.save_dir, exist_ok=True)
    os.makedirs(args.vis_dir, exist_ok=True)
    
    # Setup logging
    logger = setup_logging(args.log_dir)
    logger.info(f'Starting training with args: {args}')
    
    # Device
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    logger.info(f'Using device: {device}')
    
    # Dataset and DataLoader
    train_transform = get_transform('train')
    val_transform = get_transform('val')
    
    train_dataset = PoseTrackDataset(
        data_root=args.data_root,
        split='train',
        transform=train_transform,
        sequence_length=1  # For now, focus on single frame pose estimation
    )
    
    val_dataset = PoseTrackDataset(
        data_root=args.data_root,
        split='val',
        transform=val_transform,
        sequence_length=1
    )
    
    train_loader = DataLoader(
        train_dataset,
        batch_size=args.batch_size,
        shuffle=True,
        num_workers=4,
        collate_fn=collate_fn,
        pin_memory=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=4,
        collate_fn=collate_fn,
        pin_memory=True
    )
    
    logger.info(f'Train dataset: {len(train_dataset)} samples')
    logger.info(f'Val dataset: {len(val_dataset)} samples')
    
    # Model
    model = MPPET_RLE()
    model = model.to(device)
    
    # Loss function
    criterion = RLELoss()
    
    # Optimizer
    optimizer = optim.AdamW(model.parameters(), lr=args.lr, weight_decay=args.weight_decay)
    
    # Learning rate scheduler
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=args.epochs)
    
    # Resume from checkpoint
    start_epoch = 0
    if args.resume:
        if os.path.isfile(args.resume):
            logger.info(f'Loading checkpoint {args.resume}')
            checkpoint = torch.load(args.resume, map_location=device)
            model.load_state_dict(checkpoint['model_state_dict'])
            optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            start_epoch = checkpoint['epoch'] + 1
            logger.info(f'Resumed from epoch {start_epoch}')
        else:
            logger.warning(f'Checkpoint {args.resume} not found')
    
    # Training loop
    best_loss = float('inf')
    
    for epoch in range(start_epoch, args.epochs):
        logger.info(f'Starting epoch {epoch}/{args.epochs}')
        
        # Train
        train_loss, train_pose_loss = train_epoch(
            model, train_loader, optimizer, criterion, device, logger, epoch
        )
        
        # Validate
        val_metrics = validate_epoch(
            model, val_loader, device, logger, epoch, 
            vis_dir=args.vis_dir if epoch % 10 == 0 else None
        )
        
        # Update learning rate
        scheduler.step()
        
        # Save checkpoint
        if train_loss < best_loss:
            best_loss = train_loss
            checkpoint = {
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'train_loss': train_loss,
                'best_loss': best_loss
            }
            
            save_path = os.path.join(args.save_dir, 'best_model.pth')
            torch.save(checkpoint, save_path)
            logger.info(f'Saved best model with loss {best_loss:.4f}')
        
        # Save regular checkpoint
        if epoch % 10 == 0:
            checkpoint = {
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'train_loss': train_loss,
                'best_loss': best_loss
            }
            
            save_path = os.path.join(args.save_dir, f'checkpoint_epoch_{epoch}.pth')
            torch.save(checkpoint, save_path)
            logger.info(f'Saved checkpoint for epoch {epoch}')
    
    logger.info('Training completed!')


if __name__ == '__main__':
    main()
