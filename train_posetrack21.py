"""
Training script for MPPET-RLE with real PoseTrack21 data
"""

import os
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import argparse
from tqdm import tqdm
import logging
from datetime import datetime

from simple_rle_pose import SimpleRegress<PERSON>low
from RLE_regression_loss import R<PERSON>Loss, AdaptiveRLELoss
from datasets.posetrack_dataset import PoseTrackDataset, get_transform, collate_fn
from utils.posetrack21_metrics import PoseTrack21Evaluator
import cv2


def setup_logging(log_dir):
    """Setup logging configuration."""
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, f'posetrack21_training_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    return logging.getLogger(__name__)


def train_epoch(model, dataloader, optimizer, criterion, device, logger, epoch):
    """Train for one epoch with PoseTrack21 data."""
    model.train()
    
    total_loss = 0.0
    num_batches = 0
    num_samples = 0
    
    pbar = tqdm(dataloader, desc=f'Epoch {epoch}')
    
    for batch_idx, batch_data in enumerate(pbar):
        try:
            # Extract data
            images = batch_data['images']
            poses = batch_data['poses']
            pose_weights = batch_data['pose_weights']
            
            if len(images) == 0:
                continue
                
            # Move to device
            images = images.to(device)
            poses = poses.to(device)
            pose_weights = pose_weights.to(device)
            
            # Prepare labels
            labels = {
                'poses': poses,
                'pose_weights': pose_weights
            }
            
            optimizer.zero_grad()
            
            # Forward pass
            outputs = model(images, labels)
            
            # Compute loss
            loss = criterion(outputs, labels)
            
            # Backward pass
            loss.backward()
            
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            # Update statistics
            total_loss += loss.item()
            num_batches += 1
            num_samples += len(images)
            
            # Update progress bar
            pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Samples': num_samples
            })
            
        except Exception as e:
            logger.error(f"Error in batch {batch_idx}: {str(e)}")
            continue
    
    avg_loss = total_loss / max(num_batches, 1)
    logger.info(f'Epoch {epoch} - Avg Loss: {avg_loss:.4f}, Samples: {num_samples}')
    
    return avg_loss


def validate_epoch(model, dataloader, device, logger, epoch, evaluator):
    """Validate for one epoch with PoseTrack21 metrics."""
    model.eval()
    
    total_samples = 0
    total_val_loss = 0.0
    num_batches = 0
    
    # Reset evaluator
    evaluator.reset()
    
    # Create validation criterion
    val_criterion = RLELoss(use_target_weight=True, size_average=True)
    
    with torch.no_grad():
        pbar = tqdm(dataloader, desc=f'Validation Epoch {epoch}')
        
        for batch_idx, batch_data in enumerate(pbar):
            try:
                # Extract data
                images = batch_data['images']
                poses = batch_data['poses']
                pose_weights = batch_data['pose_weights']
                frame_info = batch_data['frame_info']
                
                if len(images) == 0:
                    continue
                    
                # Move to device
                images = images.to(device)
                poses = poses.to(device)
                pose_weights = pose_weights.to(device)
                
                # Prepare labels
                labels = {
                    'poses': poses,
                    'pose_weights': pose_weights
                }
                
                # Forward pass
                outputs = model(images)
                
                # Compute validation loss
                val_loss = val_criterion(outputs, labels)
                total_val_loss += val_loss.item()
                num_batches += 1
                
                # Extract predictions for evaluation
                pred_poses = outputs.pred_jts.cpu().numpy()
                pred_scores = outputs.maxvals.cpu().numpy()
                gt_poses = poses.cpu().numpy()
                gt_weights = pose_weights.cpu().numpy()
                
                # Prepare data for evaluator
                predictions = {
                    'poses': [pred_poses[i:i+1] for i in range(len(pred_poses))],
                    'scores': [pred_scores[i:i+1] for i in range(len(pred_scores))],
                    'track_ids': [list(range(len(pred_poses)))]
                }
                
                ground_truth = {
                    'poses': [gt_poses[i:i+1] for i in range(len(gt_poses))],
                    'track_ids': [list(range(len(gt_poses)))],
                    'visibility': [gt_weights[i:i+1, :, 0] for i in range(len(gt_weights))]  # Use first channel as visibility
                }
                
                # Add to evaluator
                evaluator.add_batch(predictions, ground_truth, frame_info)
                
                total_samples += len(images)
                
                # Update progress bar
                pbar.set_postfix({
                    'Val Loss': f'{val_loss.item():.4f}',
                    'Samples': total_samples
                })
                
            except Exception as e:
                logger.error(f"Error in validation batch {batch_idx}: {str(e)}")
                continue
    
    avg_val_loss = total_val_loss / max(num_batches, 1)
    
    # Compute evaluation metrics
    eval_results = evaluator.evaluate()
    
    logger.info(f'Validation Epoch {epoch} - Avg Loss: {avg_val_loss:.4f}, Samples: {total_samples}')
    logger.info(f'PCK: {eval_results["PCK"]:.3f}, mOKS: {eval_results["mOKS"]:.3f}')
    
    return {
        'total_samples': total_samples, 
        'avg_loss': avg_val_loss,
        'PCK': eval_results['PCK'],
        'mOKS': eval_results['mOKS'],
        'eval_results': eval_results
    }


def main():
    parser = argparse.ArgumentParser(description='Train MPPET-RLE with PoseTrack21')
    parser.add_argument('--data_root', type=str, default='datasets/PoseTrack21',
                       help='Path to PoseTrack21 dataset')
    parser.add_argument('--batch_size', type=int, default=8, help='Batch size')
    parser.add_argument('--epochs', type=int, default=50, help='Number of epochs')
    parser.add_argument('--lr', type=float, default=1e-4, help='Learning rate')
    parser.add_argument('--weight_decay', type=float, default=1e-4, help='Weight decay')
    parser.add_argument('--save_dir', type=str, default='checkpoints_posetrack21', help='Save directory')
    parser.add_argument('--log_dir', type=str, default='logs_posetrack21', help='Log directory')
    parser.add_argument('--device', type=str, default='cuda', help='Device to use')
    parser.add_argument('--eval_freq', type=int, default=5, help='Evaluation frequency (epochs)')
    
    args = parser.parse_args()
    
    # Setup directories
    os.makedirs(args.save_dir, exist_ok=True)
    
    # Setup logging
    logger = setup_logging(args.log_dir)
    logger.info(f'Starting PoseTrack21 training with args: {args}')
    
    # Device
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    logger.info(f'Using device: {device}')
    
    # Dataset configuration
    input_size = (192, 256)  # (width, height)
    
    train_transform = get_transform('train', input_size)
    val_transform = get_transform('val', input_size)
    
    train_dataset = PoseTrackDataset(
        data_root=args.data_root,
        split='train',
        transform=train_transform,
        sequence_length=1,
        input_size=input_size,
        normalize_coords=True,
        use_gt_bbox=True
    )
    
    val_dataset = PoseTrackDataset(
        data_root=args.data_root,
        split='val',
        transform=val_transform,
        sequence_length=1,
        input_size=input_size,
        normalize_coords=True,
        use_gt_bbox=True
    )
    
    train_loader = DataLoader(
        train_dataset,
        batch_size=args.batch_size,
        shuffle=True,
        num_workers=4,
        collate_fn=collate_fn,
        pin_memory=True,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=4,
        collate_fn=collate_fn,
        pin_memory=True,
        drop_last=False
    )
    
    logger.info(f'Train dataset: {len(train_dataset)} samples')
    logger.info(f'Val dataset: {len(val_dataset)} samples')
    
    # Model
    model = SimpleRegressFlow(
        num_joints=17,
        input_size=input_size,
        normalize_coords=True,
        backbone='resnet50',
        pretrained=True
    )
    model = model.to(device)
    
    # Loss function
    criterion = AdaptiveRLELoss(
        use_target_weight=True,
        loss_weight=1.0,
        residual_weight=1.0,
        nf_weight=0.5,
        size_average=True
    )
    
    # Optimizer
    optimizer = optim.AdamW(model.parameters(), lr=args.lr, weight_decay=args.weight_decay)
    
    # Learning rate scheduler
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=args.epochs)
    
    # Evaluator
    evaluator = PoseTrack21Evaluator(num_joints=17)
    
    # Training loop
    best_pck = 0.0
    
    for epoch in range(args.epochs):
        logger.info(f'Starting epoch {epoch}/{args.epochs}')
        
        # Train
        train_loss = train_epoch(
            model, train_loader, optimizer, criterion, device, logger, epoch
        )
        
        # Validate
        if epoch % args.eval_freq == 0 or epoch == args.epochs - 1:
            val_metrics = validate_epoch(
                model, val_loader, device, logger, epoch, evaluator
            )
            
            # Print detailed results
            evaluator.print_results(val_metrics['eval_results'])
            
            # Save best model
            if val_metrics['PCK'] > best_pck:
                best_pck = val_metrics['PCK']
                checkpoint = {
                    'epoch': epoch,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'train_loss': train_loss,
                    'val_metrics': val_metrics,
                    'best_pck': best_pck
                }
                
                save_path = os.path.join(args.save_dir, 'best_model.pth')
                torch.save(checkpoint, save_path)
                logger.info(f'Saved best model with PCK {best_pck:.3f}')
        
        # Update learning rate
        scheduler.step()
        
        # Save regular checkpoint
        if epoch % 10 == 0:
            checkpoint = {
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'train_loss': train_loss,
                'best_pck': best_pck
            }
            
            save_path = os.path.join(args.save_dir, f'checkpoint_epoch_{epoch}.pth')
            torch.save(checkpoint, save_path)
    
    logger.info('Training completed!')
    logger.info(f'Best PCK achieved: {best_pck:.3f}')


if __name__ == '__main__':
    main()
