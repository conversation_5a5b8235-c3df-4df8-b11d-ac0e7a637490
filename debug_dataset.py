"""
Debug script for PoseTrack21 dataset loading
"""

import os
import sys
sys.path.append('.')

from datasets.posetrack_dataset import PoseTrackDataset, get_transform

def debug_dataset():
    print("🔍 Debugging PoseTrack21 Dataset Loading...")
    
    # Check data structure first
    data_root = 'datasets/PoseTrack21'
    
    print(f"Data root: {data_root}")
    print(f"Exists: {os.path.exists(data_root)}")
    
    # Check train data
    train_dir = os.path.join(data_root, 'data', 'posetrack_data', 'train')
    print(f"Train dir: {train_dir}")
    print(f"Exists: {os.path.exists(train_dir)}")
    
    if os.path.exists(train_dir):
        json_files = [f for f in os.listdir(train_dir) if f.endswith('.json')]
        print(f"JSON files: {len(json_files)}")
        
        if json_files:
            # Check first file
            first_file = os.path.join(train_dir, json_files[0])
            print(f"First file: {first_file}")
            
            import json
            with open(first_file, 'r') as f:
                data = json.load(f)
            
            print(f"Keys in data: {list(data.keys())}")
            print(f"Number of images: {len(data.get('images', []))}")
            print(f"Number of annotations: {len(data.get('annotations', []))}")
            
            # Check image structure
            if data.get('images'):
                img = data['images'][0]
                print(f"First image keys: {list(img.keys())}")
                print(f"First image file_name: {img.get('file_name')}")
                
                # Check if image file exists
                img_path = os.path.join(data_root, img.get('file_name', ''))
                alt_path = os.path.join(data_root, 'data', img.get('file_name', ''))
                print(f"Image path: {img_path} (exists: {os.path.exists(img_path)})")
                print(f"Alt path: {alt_path} (exists: {os.path.exists(alt_path)})")
            
            # Check annotation structure
            if data.get('annotations'):
                ann = data['annotations'][0]
                print(f"First annotation keys: {list(ann.keys())}")
                print(f"Category ID: {ann.get('category_id')}")
                print(f"Image ID: {ann.get('image_id')}")
    
    # Try creating dataset
    print("\n" + "="*50)
    print("Creating dataset...")
    
    try:
        transform = get_transform('train')
        dataset = PoseTrackDataset(
            data_root=data_root,
            split='train',
            transform=transform,
            sequence_length=1,
            input_size=(192, 256),
            normalize_coords=True
        )
        print(f"✅ Dataset created successfully with {len(dataset)} samples")
        
        if len(dataset) > 0:
            # Try getting first sample
            sample = dataset[0]
            print(f"✅ First sample loaded successfully")
            print(f"Sample keys: {list(sample.keys())}")
            
    except Exception as e:
        print(f"❌ Error creating dataset: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    debug_dataset()
