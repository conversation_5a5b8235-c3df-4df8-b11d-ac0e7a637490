import os
import json
import torch
import numpy as np
from torch.utils.data import Dataset
import cv2
from PIL import Image
import torchvision.transforms as transforms


class PoseTrackDataset(Dataset):
    """
    PoseTrack21 dataset for multi-person pose estimation and tracking
    """
    def __init__(self, data_root, split='train', transform=None, sequence_length=2):
        self.data_root = data_root
        self.split = split
        self.transform = transform
        self.sequence_length = sequence_length
        
        # PoseTrack21 keypoint names
        self.keypoint_names = [
            "nose", "head_bottom", "head_top", "left_ear", "right_ear",
            "left_shoulder", "right_shoulder", "left_elbow", "right_elbow",
            "left_wrist", "right_wrist", "left_hip", "right_hip",
            "left_knee", "right_knee", "left_ankle", "right_ankle"
        ]
        self.num_joints = len(self.keypoint_names)
        
        # Load annotations
        self.annotations = self._load_annotations()
        self.sequences = self._organize_sequences()
        
        # Create training samples
        self.samples = self._create_samples()
        
        print(f"Loaded {len(self.samples)} samples from {split} split")
    
    def _load_annotations(self):
        """Load PoseTrack21 annotations"""
        annotation_dir = os.path.join(self.data_root, 'posetrack_data', self.split)
        annotations = {}
        
        for json_file in os.listdir(annotation_dir):
            if json_file.endswith('.json'):
                json_path = os.path.join(annotation_dir, json_file)
                with open(json_path, 'r') as f:
                    data = json.load(f)
                
                vid_id = json_file.split('_')[0]
                annotations[vid_id] = data
        
        return annotations
    
    def _organize_sequences(self):
        """Organize annotations by video sequences"""
        sequences = {}
        
        for vid_id, annotation in self.annotations.items():
            images = annotation['images']
            annotations_list = annotation['annotations']
            
            # Create image_id to annotation mapping
            image_annotations = {}
            for ann in annotations_list:
                image_id = ann['image_id']
                if image_id not in image_annotations:
                    image_annotations[image_id] = []
                image_annotations[image_id].append(ann)
            
            # Sort images by frame number
            images.sort(key=lambda x: x['id'])
            
            sequences[vid_id] = {
                'images': images,
                'annotations': image_annotations
            }
        
        return sequences
    
    def _create_samples(self):
        """Create training samples (sequences of frames)"""
        samples = []
        
        for vid_id, sequence in self.sequences.items():
            images = sequence['images']
            annotations = sequence['annotations']
            
            # Create consecutive frame pairs for tracking
            for i in range(len(images) - self.sequence_length + 1):
                frame_sequence = []
                valid_sequence = True
                
                for j in range(self.sequence_length):
                    frame_idx = i + j
                    image_info = images[frame_idx]
                    image_id = image_info['id']
                    
                    # Check if frame has annotations
                    if image_id not in annotations or not image_info.get('has_labeled_person', False):
                        valid_sequence = False
                        break
                    
                    frame_sequence.append({
                        'vid_id': vid_id,
                        'image_info': image_info,
                        'annotations': annotations[image_id]
                    })
                
                if valid_sequence:
                    samples.append(frame_sequence)
        
        return samples
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        sequence = self.samples[idx]
        
        # Load images and annotations for the sequence
        images = []
        all_annotations = []
        
        for frame_data in sequence:
            # Load image
            image_path = os.path.join(self.data_root, frame_data['image_info']['file_name'])
            image = cv2.imread(image_path)
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            images.append(image)
            all_annotations.append(frame_data['annotations'])
        
        # Process for multi-person tracking
        return self._process_sequence(images, all_annotations)
    
    def _process_sequence(self, images, all_annotations):
        """Process sequence for multi-person pose tracking"""
        # For now, we'll focus on the first frame for pose estimation
        # and use the second frame for temporal tracking supervision
        
        current_image = images[0]
        current_annotations = all_annotations[0]
        
        if len(images) > 1:
            next_image = images[1]
            next_annotations = all_annotations[1]
        else:
            next_image = current_image
            next_annotations = current_annotations
        
        # Extract person crops and poses from current frame
        person_data = self._extract_person_data(current_image, current_annotations)
        
        # Extract temporal targets if next frame is available
        temporal_targets = self._extract_temporal_targets(
            current_annotations, next_annotations, current_image.shape[:2]
        )
        
        # Apply transforms
        if self.transform:
            for i, (crop, pose, box) in enumerate(person_data):
                # Convert numpy array to PIL Image for transform
                if isinstance(crop, np.ndarray):
                    crop_pil = Image.fromarray(crop)
                    crop_transformed = self.transform(crop_pil)
                else:
                    crop_transformed = self.transform(crop)
                person_data[i] = (crop_transformed, pose, box)
        
        return {
            'images': person_data,
            'temporal_targets': temporal_targets,
            'frame_info': {
                'vid_id': self.samples[0][0]['vid_id'] if len(self.samples) > 0 else '',
                'frame_id': current_annotations[0]['image_id'] if current_annotations else 0
            }
        }
    
    def _extract_person_data(self, image, annotations):
        """Extract person crops and pose data"""
        person_data = []
        
        for ann in annotations:
            if ann['category_id'] != 1:  # Only person category
                continue
            
            # Get bounding box
            bbox = ann['bbox']  # [x, y, width, height]
            x, y, w, h = bbox
            x1, y1, x2, y2 = int(x), int(y), int(x + w), int(y + h)
            
            # Add padding
            img_h, img_w = image.shape[:2]
            x1 = max(0, x1 - 10)
            y1 = max(0, y1 - 10)
            x2 = min(img_w, x2 + 10)
            y2 = min(img_h, y2 + 10)
            
            # Extract crop
            crop = image[y1:y2, x1:x2]
            crop_resized = cv2.resize(crop, (192, 256))
            
            # Get keypoints
            keypoints = np.array(ann['keypoints']).reshape(-1, 3)  # [x, y, visibility]
            
            # Convert to crop coordinates
            pose_in_crop = keypoints.copy()
            pose_in_crop[:, 0] = (pose_in_crop[:, 0] - x1) * (192 / (x2 - x1))
            pose_in_crop[:, 1] = (pose_in_crop[:, 1] - y1) * (256 / (y2 - y1))
            
            # Only keep x, y coordinates
            pose_coords = pose_in_crop[:, :2]  # Shape: (17, 2)
            
            person_data.append((crop_resized, pose_coords, [x1, y1, x2, y2]))
        
        return person_data
    
    def _extract_temporal_targets(self, current_annotations, next_annotations, image_shape):
        """Extract temporal tracking targets"""
        temporal_targets = []
        
        # Match persons between frames using track_id
        current_tracks = {ann['track_id']: ann for ann in current_annotations if 'track_id' in ann}
        next_tracks = {ann['track_id']: ann for ann in next_annotations if 'track_id' in ann}
        
        for track_id, current_ann in current_tracks.items():
            if track_id in next_tracks:
                next_ann = next_tracks[track_id]
                
                # Compute temporal offset (movement between frames)
                current_keypoints = np.array(current_ann['keypoints']).reshape(-1, 3)
                next_keypoints = np.array(next_ann['keypoints']).reshape(-1, 3)
                
                # Compute offset for each keypoint
                temporal_offset = next_keypoints[:, :2] - current_keypoints[:, :2]
                
                temporal_targets.append({
                    'track_id': track_id,
                    'temporal_offset': temporal_offset
                })
        
        return temporal_targets


def get_transform(split='train'):
    """Get data transforms"""
    if split == 'train':
        transform = transforms.Compose([
            transforms.ToPILImage(),
            transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
    else:
        transform = transforms.Compose([
            transforms.ToPILImage(),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
    
    return transform
