"""
Real NVP (Non-Volume Preserving) normalizing flow implementation
Based on "Density estimation using Real NVP" paper
"""

import torch
import torch.nn as nn
import torch.distributions as distributions


class RealNVP(nn.Module):
    """Real NVP normalizing flow model."""
    
    def __init__(self, nets, nett, masks, prior):
        """
        Initialize Real NVP model.
        
        Args:
            nets: Function that returns scale network
            nett: Function that returns translation network  
            masks: Binary masks for coupling layers
            prior: Prior distribution
        """
        super(RealNVP, self).__init__()
        
        self.prior = prior
        self.masks = nn.Parameter(masks, requires_grad=False)
        self.t = torch.nn.ModuleList([nett() for _ in range(len(masks))])
        self.s = torch.nn.ModuleList([nets() for _ in range(len(masks))])
        
    def g(self, z):
        """Forward transformation from latent to data space."""
        x = z
        for i in range(len(self.t)):
            x_ = x * self.masks[i]
            s = self.s[i](x_) * (1 - self.masks[i])
            t = self.t[i](x_) * (1 - self.masks[i])
            x = x_ + (1 - self.masks[i]) * (x * torch.exp(s) + t)
        return x
    
    def f(self, x):
        """Inverse transformation from data to latent space."""
        log_det_J, z = x.new_zeros(x.shape[0]), x
        for i in reversed(range(len(self.t))):
            z_ = self.masks[i] * z
            s = self.s[i](z_) * (1 - self.masks[i])
            t = self.t[i](z_) * (1 - self.masks[i])
            z = (1 - self.masks[i]) * (z - t) * torch.exp(-s) + z_
            log_det_J -= s.sum(dim=1)
        return z, log_det_J
    
    def log_prob(self, x):
        """Compute log probability of x."""
        z, log_det_J = self.f(x)
        log_prob_z = self.prior.log_prob(z)
        return log_prob_z + log_det_J
    
    def sample(self, batchSize):
        """Sample from the model."""
        z = self.prior.sample((batchSize,))
        x = self.g(z)
        return x


class CouplingLayer(nn.Module):
    """Single coupling layer for Real NVP."""
    
    def __init__(self, mask, scale_net, translate_net):
        super(CouplingLayer, self).__init__()
        self.mask = mask
        self.scale_net = scale_net
        self.translate_net = translate_net
        
    def forward(self, x, reverse=False):
        if not reverse:
            # Forward pass
            x_masked = x * self.mask
            scale = self.scale_net(x_masked)
            translate = self.translate_net(x_masked)
            
            y = x_masked + (1 - self.mask) * (x * torch.exp(scale) + translate)
            log_det = scale.sum(dim=-1)
            
            return y, log_det
        else:
            # Reverse pass
            y_masked = x * self.mask
            scale = self.scale_net(y_masked)
            translate = self.translate_net(y_masked)
            
            x = y_masked + (1 - self.mask) * (x - translate) * torch.exp(-scale)
            log_det = -scale.sum(dim=-1)
            
            return x, log_det


def create_real_nvp(input_dim, hidden_dim, num_layers, num_blocks=2):
    """
    Create a Real NVP model with specified architecture.
    
    Args:
        input_dim: Dimension of input data
        hidden_dim: Hidden dimension for coupling networks
        num_layers: Number of coupling layers
        num_blocks: Number of hidden layers in each coupling network
    
    Returns:
        RealNVP model
    """
    def create_net():
        layers = []
        layers.append(nn.Linear(input_dim, hidden_dim))
        layers.append(nn.ReLU())
        
        for _ in range(num_blocks - 1):
            layers.append(nn.Linear(hidden_dim, hidden_dim))
            layers.append(nn.ReLU())
            
        layers.append(nn.Linear(hidden_dim, input_dim))
        return nn.Sequential(*layers)
    
    def create_scale_net():
        net = create_net()
        # Initialize last layer to output small values for stability
        nn.init.zeros_(net[-1].weight)
        nn.init.zeros_(net[-1].bias)
        return net
    
    def create_translate_net():
        return create_net()
    
    # Create alternating masks
    masks = []
    for i in range(num_layers):
        mask = torch.zeros(input_dim)
        mask[i % 2::2] = 1
        masks.append(mask)
    
    masks = torch.stack(masks)
    
    # Prior distribution
    prior = distributions.MultivariateNormal(torch.zeros(input_dim), torch.eye(input_dim))
    
    return RealNVP(create_scale_net, create_translate_net, masks, prior)
