2025-06-11 23:50:05,644 - MPPET_RLE - INFO - ============================================================
2025-06-11 23:50:05,644 - MPPET_RLE - INFO - MPPET-RLE FULL TRAINING STARTED
2025-06-11 23:50:05,644 - MPPET_RLE - INFO - ============================================================
2025-06-11 23:50:05,644 - MPPET_RLE - INFO - Training configuration: {'batch_size': 12, 'epochs': 50, 'lr': 0.0001, 'weight_decay': 0.0001, 'save_dir': 'checkpoints_full', 'log_dir': 'logs_full', 'vis_dir': 'visualizations_full', 'device': 'cuda', 'num_samples': 1500, 'difficulty': 'medium', 'resume': None}
2025-06-11 23:50:05,647 - MPPET_RLE - INFO - Using device: cuda
2025-06-11 23:50:05,647 - MPPET_RLE - INFO - Creating datasets with 1500 samples, difficulty: medium
2025-06-11 23:50:05,961 - MPPET_RLE - INFO - Model: 26,165,892 total parameters, 26,165,892 trainable
2025-06-11 23:50:05,962 - MPPET_RLE - INFO - Starting training loop...
2025-06-11 23:50:05,962 - MPPET_RLE - INFO - 
==================== EPOCH 1/50 ====================
2025-06-11 23:50:06,315 - MPPET_RLE - INFO - Epoch 0, Batch 0: Loss=1217153.5000, PoseError=185.4620, AvgSigma=0.5000
