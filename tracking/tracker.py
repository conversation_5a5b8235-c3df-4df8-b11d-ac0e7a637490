"""
Main tracking pipeline for MPPET-RLE
"""

import torch
import torch.nn as nn
import numpy as np

from .track_manager import Track<PERSON>anager
from .association import DistributionAwareAssociation, ConfidenceBasedAssociation
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from builder import TRACKER


@TRACKER.register_module
class MPPETTracker(nn.Module):
    """Multi-Person Pose Estimation and Tracking with RLE uncertainty."""
    
    def __init__(self, 
                 association_type='distribution_aware',
                 motion_model='kalman',
                 max_age=30,
                 min_hits=3,
                 bbox_weight=0.3,
                 pose_weight=0.7,
                 max_distance=100.0,
                 uncertainty_threshold=10.0,
                 confidence_threshold=0.5):
        """
        Initialize MPPET tracker.
        
        Args:
            association_type: Type of association ('distribution_aware' or 'confidence_based')
            motion_model: Motion model type ('kalman' or 'simple')
            max_age: Maximum age of tracks without detection
            min_hits: Minimum hits before track confirmation
            bbox_weight: Weight for bounding box in association
            pose_weight: Weight for pose in association
            max_distance: Maximum association distance
            uncertainty_threshold: Threshold for uncertain keypoints
            confidence_threshold: Confidence threshold for association
        """
        super(MPPETTracker, self).__init__()
        
        # Track manager
        self.track_manager = TrackManager(
            max_age=max_age,
            min_hits=min_hits,
            motion_model=motion_model
        )
        
        # Association module
        if association_type == 'distribution_aware':
            self.association = DistributionAwareAssociation(
                bbox_weight=bbox_weight,
                pose_weight=pose_weight,
                max_distance=max_distance,
                uncertainty_threshold=uncertainty_threshold
            )
        elif association_type == 'confidence_based':
            self.association = ConfidenceBasedAssociation(
                bbox_weight=bbox_weight,
                pose_weight=pose_weight,
                max_distance=max_distance,
                confidence_threshold=confidence_threshold
            )
        else:
            raise ValueError(f"Unknown association type: {association_type}")
        
        self.frame_count = 0
    
    def forward(self, detections):
        """
        Process detections for current frame.
        
        Args:
            detections: List of detection dictionaries with keys:
                - 'bbox': [x1, y1, x2, y2] bounding box
                - 'pose_mu': (num_joints, 2) keypoint positions
                - 'pose_sigma': (num_joints, 2) keypoint uncertainties
                - 'score': detection confidence (optional)
        
        Returns:
            tracking_results: List of track results
        """
        self.frame_count += 1
        
        # Predict all existing tracks
        self.track_manager.predict()
        
        # Get current track states for association
        track_states = self.track_manager.get_track_states()
        
        # Perform association
        matches, unmatched_tracks, unmatched_detections = self.association.associate(
            track_states, detections
        )
        
        # Update track manager
        self.track_manager.update(matches, unmatched_detections, detections)
        
        # Get tracking results
        tracking_results = self.track_manager.get_track_results()
        
        return tracking_results
    
    def reset(self):
        """Reset tracker state."""
        self.track_manager.reset()
        self.frame_count = 0
    
    def get_statistics(self):
        """Get tracking statistics."""
        stats = self.track_manager.get_statistics()
        stats['frame_count'] = self.frame_count
        return stats


@TRACKER.register_module
class SimpleTracker(nn.Module):
    """Simplified tracker for baseline comparison."""
    
    def __init__(self, max_age=30, min_hits=3, iou_threshold=0.3):
        super(SimpleTracker, self).__init__()
        
        self.max_age = max_age
        self.min_hits = min_hits
        self.iou_threshold = iou_threshold
        
        self.tracks = []
        self.next_track_id = 1
        self.frame_count = 0
    
    def forward(self, detections):
        """Simple IoU-based tracking."""
        self.frame_count += 1
        
        if len(self.tracks) == 0:
            # Initialize tracks
            for detection in detections:
                self._create_track(detection)
            return self._get_results()
        
        # Compute IoU matrix
        iou_matrix = self._compute_iou_matrix(detections)
        
        # Simple greedy assignment
        matches, unmatched_detections = self._greedy_assignment(iou_matrix)
        
        # Update matched tracks
        for track_idx, det_idx in matches:
            self.tracks[track_idx]['age'] = 0
            self.tracks[track_idx]['bbox'] = detections[det_idx]['bbox']
            self.tracks[track_idx]['pose_mu'] = detections[det_idx]['pose_mu']
            self.tracks[track_idx]['pose_sigma'] = detections[det_idx]['pose_sigma']
            self.tracks[track_idx]['hits'] += 1
        
        # Age unmatched tracks
        matched_track_indices = set([m[0] for m in matches])
        for i in range(len(self.tracks)):
            if i not in matched_track_indices:
                self.tracks[i]['age'] += 1
        
        # Remove old tracks
        self.tracks = [t for t in self.tracks if t['age'] <= self.max_age]
        
        # Create new tracks
        for det_idx in unmatched_detections:
            self._create_track(detections[det_idx])
        
        return self._get_results()
    
    def _compute_iou_matrix(self, detections):
        """Compute IoU matrix between tracks and detections."""
        if len(self.tracks) == 0 or len(detections) == 0:
            return np.array([]).reshape(len(self.tracks), len(detections))
        
        iou_matrix = np.zeros((len(self.tracks), len(detections)))
        
        for i, track in enumerate(self.tracks):
            for j, detection in enumerate(detections):
                iou = self._compute_iou(track['bbox'], detection['bbox'])
                iou_matrix[i, j] = iou
        
        return iou_matrix
    
    def _compute_iou(self, bbox1, bbox2):
        """Compute IoU between two bounding boxes."""
        # Convert to numpy if needed
        if torch.is_tensor(bbox1):
            bbox1 = bbox1.cpu().numpy()
        if torch.is_tensor(bbox2):
            bbox2 = bbox2.cpu().numpy()
        
        x1_inter = max(bbox1[0], bbox2[0])
        y1_inter = max(bbox1[1], bbox2[1])
        x2_inter = min(bbox1[2], bbox2[2])
        y2_inter = min(bbox1[3], bbox2[3])
        
        if x2_inter <= x1_inter or y2_inter <= y1_inter:
            return 0.0
        
        inter_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)
        area1 = (bbox1[2] - bbox1[0]) * (bbox1[3] - bbox1[1])
        area2 = (bbox2[2] - bbox2[0]) * (bbox2[3] - bbox2[1])
        union_area = area1 + area2 - inter_area
        
        return inter_area / union_area if union_area > 0 else 0.0
    
    def _greedy_assignment(self, iou_matrix):
        """Simple greedy assignment based on IoU."""
        matches = []
        unmatched_detections = list(range(iou_matrix.shape[1]))
        
        if iou_matrix.size == 0:
            return matches, unmatched_detections
        
        # Find best matches greedily
        while True:
            # Find maximum IoU
            max_iou = np.max(iou_matrix)
            if max_iou < self.iou_threshold:
                break
            
            # Find indices of maximum IoU
            track_idx, det_idx = np.unravel_index(np.argmax(iou_matrix), iou_matrix.shape)
            
            # Add match
            matches.append((track_idx, det_idx))
            unmatched_detections.remove(det_idx)
            
            # Remove matched row and column
            iou_matrix[track_idx, :] = 0
            iou_matrix[:, det_idx] = 0
        
        return matches, unmatched_detections
    
    def _create_track(self, detection):
        """Create new track."""
        track = {
            'track_id': self.next_track_id,
            'bbox': detection['bbox'],
            'pose_mu': detection['pose_mu'],
            'pose_sigma': detection['pose_sigma'],
            'age': 0,
            'hits': 1
        }
        self.tracks.append(track)
        self.next_track_id += 1
    
    def _get_results(self):
        """Get tracking results."""
        results = []
        for track in self.tracks:
            if track['hits'] >= self.min_hits:
                result = {
                    'track_id': track['track_id'],
                    'bbox': track['bbox'],
                    'pose': track['pose_mu'],
                    'confidence': min(track['hits'] / self.min_hits, 1.0),
                    'frame_id': self.frame_count
                }
                results.append(result)
        return results
    
    def reset(self):
        """Reset tracker."""
        self.tracks = []
        self.next_track_id = 1
        self.frame_count = 0


def build_tracker(cfg):
    """Build tracker from config."""
    tracker_type = cfg.get('type', 'MPPETTracker')
    
    if tracker_type == 'MPPETTracker':
        return MPPETTracker(
            association_type=cfg.get('association_type', 'distribution_aware'),
            motion_model=cfg.get('motion_model', 'kalman'),
            max_age=cfg.get('max_age', 30),
            min_hits=cfg.get('min_hits', 3),
            bbox_weight=cfg.get('bbox_weight', 0.3),
            pose_weight=cfg.get('pose_weight', 0.7),
            max_distance=cfg.get('max_distance', 100.0),
            uncertainty_threshold=cfg.get('uncertainty_threshold', 10.0),
            confidence_threshold=cfg.get('confidence_threshold', 0.5)
        )
    elif tracker_type == 'SimpleTracker':
        return SimpleTracker(
            max_age=cfg.get('max_age', 30),
            min_hits=cfg.get('min_hits', 3),
            iou_threshold=cfg.get('iou_threshold', 0.3)
        )
    else:
        raise ValueError(f"Unknown tracker type: {tracker_type}")
