"""
Track management for multi-person pose tracking
"""

import numpy as np
import torch
from .motion_model import KalmanMotionModel, SimpleMotionModel


class Track:
    """Individual track for a person."""
    
    def __init__(self, track_id, detection_bbox, pose_mu, pose_sigma, 
                 motion_model='kalman', max_age=30, min_hits=3):
        """
        Initialize a track.
        
        Args:
            track_id: Unique track identifier
            detection_bbox: Initial bounding box [x1, y1, x2, y2]
            pose_mu: Initial pose means (num_joints, 2)
            pose_sigma: Initial pose uncertainties (num_joints, 2)
            motion_model: Type of motion model ('kalman' or 'simple')
            max_age: Maximum frames without detection before deletion
            min_hits: Minimum detections before track is confirmed
        """
        self.track_id = track_id
        self.max_age = max_age
        self.min_hits = min_hits
        
        # Track state
        self.age = 0
        self.time_since_update = 0
        self.hit_streak = 1
        self.state = 'tentative'  # 'tentative', 'confirmed', 'deleted'
        
        # Motion model
        if motion_model == 'kalman':
            self.motion_model = KalmanMotionModel(num_joints=pose_mu.shape[0])
        else:
            self.motion_model = SimpleMotionModel(num_joints=pose_mu.shape[0])
        
        # Initialize motion model
        self.motion_model.initialize(detection_bbox, pose_mu, pose_sigma)
        
        # Store current estimates
        self.bbox = detection_bbox
        self.pose_mu = pose_mu.copy()
        self.pose_sigma = pose_sigma.copy()
        
        # History for visualization
        self.history = []
        self.history.append({
            'bbox': detection_bbox,
            'pose_mu': pose_mu.copy(),
            'pose_sigma': pose_sigma.copy()
        })
    
    def predict(self):
        """Predict next state using motion model."""
        self.age += 1
        self.time_since_update += 1
        
        # Get prediction from motion model
        predicted_pose, predicted_bbox = self.motion_model.predict()
        
        # Update current estimates with prediction
        self.pose_mu = predicted_pose
        self.bbox = predicted_bbox
        
        return predicted_pose, predicted_bbox
    
    def update(self, detection_bbox, pose_mu, pose_sigma):
        """Update track with new detection."""
        self.time_since_update = 0
        self.hit_streak += 1
        
        # Update motion model
        self.motion_model.update(detection_bbox, pose_mu, pose_sigma)
        
        # Get updated state from motion model
        current_pose, current_bbox = self.motion_model.get_current_state()
        
        # Update current estimates
        if current_pose is not None and current_bbox is not None:
            self.pose_mu = current_pose
            self.bbox = current_bbox
        else:
            # Fallback to direct assignment
            self.pose_mu = pose_mu.copy()
            self.bbox = detection_bbox
        
        self.pose_sigma = pose_sigma.copy()
        
        # Update state
        if self.state == 'tentative' and self.hit_streak >= self.min_hits:
            self.state = 'confirmed'
        
        # Add to history
        self.history.append({
            'bbox': self.bbox,
            'pose_mu': self.pose_mu.copy(),
            'pose_sigma': self.pose_sigma.copy()
        })
        
        # Limit history size
        if len(self.history) > 100:
            self.history = self.history[-50:]
    
    def mark_missed(self):
        """Mark track as missed (no detection matched)."""
        self.time_since_update += 1
        self.hit_streak = 0
        
        # Predict without update
        self.predict()
        
        # Check if track should be deleted
        if self.time_since_update > self.max_age:
            self.state = 'deleted'
    
    def is_confirmed(self):
        """Check if track is confirmed."""
        return self.state == 'confirmed'
    
    def is_deleted(self):
        """Check if track should be deleted."""
        return self.state == 'deleted'
    
    def is_tentative(self):
        """Check if track is tentative."""
        return self.state == 'tentative'
    
    def get_state(self):
        """Get current track state for association."""
        return {
            'track_id': self.track_id,
            'bbox': self.bbox,
            'pose_mu': self.pose_mu,
            'pose_sigma': self.pose_sigma,
            'confidence': self._compute_confidence()
        }
    
    def _compute_confidence(self):
        """Compute track confidence based on hit streak and uncertainty."""
        # Base confidence from hit streak
        hit_confidence = min(self.hit_streak / self.min_hits, 1.0)
        
        # Uncertainty-based confidence (lower uncertainty = higher confidence)
        avg_uncertainty = np.mean(self.pose_sigma)
        uncertainty_confidence = 1.0 / (1.0 + avg_uncertainty)
        
        # Combined confidence
        return 0.7 * hit_confidence + 0.3 * uncertainty_confidence


class TrackManager:
    """Manages multiple tracks for multi-person pose tracking."""
    
    def __init__(self, max_age=30, min_hits=3, motion_model='kalman'):
        """
        Initialize track manager.
        
        Args:
            max_age: Maximum frames without detection before track deletion
            min_hits: Minimum detections before track confirmation
            motion_model: Type of motion model to use
        """
        self.max_age = max_age
        self.min_hits = min_hits
        self.motion_model = motion_model
        
        self.tracks = []
        self.next_track_id = 1
        self.frame_count = 0
    
    def predict(self):
        """Predict all tracks to current frame."""
        for track in self.tracks:
            track.predict()
    
    def update(self, matches, unmatched_detections, detections):
        """
        Update tracks with association results.
        
        Args:
            matches: List of (track_idx, detection_idx) tuples
            unmatched_detections: List of detection indices
            detections: List of detection dictionaries
        """
        self.frame_count += 1
        
        # Update matched tracks
        for track_idx, detection_idx in matches:
            detection = detections[detection_idx]
            self.tracks[track_idx].update(
                detection['bbox'],
                detection['pose_mu'],
                detection['pose_sigma']
            )
        
        # Mark unmatched tracks as missed
        matched_track_indices = set([m[0] for m in matches])
        for i, track in enumerate(self.tracks):
            if i not in matched_track_indices:
                track.mark_missed()
        
        # Create new tracks for unmatched detections
        for detection_idx in unmatched_detections:
            detection = detections[detection_idx]
            self._create_new_track(
                detection['bbox'],
                detection['pose_mu'],
                detection['pose_sigma']
            )
        
        # Remove deleted tracks
        self.tracks = [track for track in self.tracks if not track.is_deleted()]
    
    def _create_new_track(self, detection_bbox, pose_mu, pose_sigma):
        """Create a new track from detection."""
        track = Track(
            track_id=self.next_track_id,
            detection_bbox=detection_bbox,
            pose_mu=pose_mu,
            pose_sigma=pose_sigma,
            motion_model=self.motion_model,
            max_age=self.max_age,
            min_hits=self.min_hits
        )
        
        self.tracks.append(track)
        self.next_track_id += 1
    
    def get_confirmed_tracks(self):
        """Get all confirmed tracks."""
        return [track for track in self.tracks if track.is_confirmed()]
    
    def get_all_tracks(self):
        """Get all tracks (including tentative)."""
        return self.tracks
    
    def get_track_states(self):
        """Get current states of all tracks for association."""
        return [track.get_state() for track in self.tracks]
    
    def get_track_results(self):
        """Get tracking results for evaluation."""
        results = []
        
        for track in self.get_confirmed_tracks():
            result = {
                'track_id': track.track_id,
                'bbox': track.bbox,
                'pose': track.pose_mu,
                'confidence': track._compute_confidence(),
                'frame_id': self.frame_count
            }
            results.append(result)
        
        return results
    
    def reset(self):
        """Reset track manager."""
        self.tracks = []
        self.next_track_id = 1
        self.frame_count = 0
    
    def get_statistics(self):
        """Get tracking statistics."""
        total_tracks = len(self.tracks)
        confirmed_tracks = len(self.get_confirmed_tracks())
        tentative_tracks = len([t for t in self.tracks if t.is_tentative()])
        
        return {
            'total_tracks': total_tracks,
            'confirmed_tracks': confirmed_tracks,
            'tentative_tracks': tentative_tracks,
            'frame_count': self.frame_count
        }
