"""
Distribution-aware association for multi-person pose tracking
"""

import torch
import torch.nn as nn
import numpy as np
from scipy.optimize import linear_sum_assignment
from scipy.spatial.distance import cdist


class DistributionAwareAssociation:
    """Association module that uses RLE uncertainty for robust matching."""
    
    def __init__(self, 
                 bbox_weight=0.3,
                 pose_weight=0.7,
                 max_distance=100.0,
                 uncertainty_threshold=10.0):
        """
        Initialize association module.
        
        Args:
            bbox_weight: Weight for bounding box IoU in cost computation
            pose_weight: Weight for pose similarity in cost computation
            max_distance: Maximum allowed distance for association
            uncertainty_threshold: Threshold for considering keypoints as uncertain
        """
        self.bbox_weight = bbox_weight
        self.pose_weight = pose_weight
        self.max_distance = max_distance
        self.uncertainty_threshold = uncertainty_threshold
    
    def compute_cost_matrix(self, tracks, detections):
        """
        Compute cost matrix between tracks and detections.
        
        Args:
            tracks: List of track dictionaries with 'bbox', 'pose_mu', 'pose_sigma'
            detections: List of detection dictionaries with 'bbox', 'pose_mu', 'pose_sigma'
            
        Returns:
            cost_matrix: (num_tracks, num_detections) cost matrix
        """
        if len(tracks) == 0 or len(detections) == 0:
            return np.array([]).reshape(len(tracks), len(detections))
        
        num_tracks = len(tracks)
        num_detections = len(detections)
        cost_matrix = np.full((num_tracks, num_detections), self.max_distance)
        
        for i, track in enumerate(tracks):
            for j, detection in enumerate(detections):
                cost = self._compute_pairwise_cost(track, detection)
                cost_matrix[i, j] = cost
        
        return cost_matrix
    
    def _compute_pairwise_cost(self, track, detection):
        """Compute cost between a single track and detection."""
        # Bounding box IoU cost
        bbox_cost = self._compute_bbox_cost(track['bbox'], detection['bbox'])
        
        # Pose similarity cost with uncertainty weighting
        pose_cost = self._compute_pose_cost(
            track['pose_mu'], track['pose_sigma'],
            detection['pose_mu'], detection['pose_sigma']
        )
        
        # Combined cost
        total_cost = self.bbox_weight * bbox_cost + self.pose_weight * pose_cost
        
        return total_cost
    
    def _compute_bbox_cost(self, bbox1, bbox2):
        """Compute bounding box cost (1 - IoU)."""
        # Convert to numpy if needed
        if torch.is_tensor(bbox1):
            bbox1 = bbox1.cpu().numpy()
        if torch.is_tensor(bbox2):
            bbox2 = bbox2.cpu().numpy()
        
        # Compute IoU
        x1_inter = max(bbox1[0], bbox2[0])
        y1_inter = max(bbox1[1], bbox2[1])
        x2_inter = min(bbox1[2], bbox2[2])
        y2_inter = min(bbox1[3], bbox2[3])
        
        if x2_inter <= x1_inter or y2_inter <= y1_inter:
            return 1.0  # No overlap
        
        inter_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)
        
        area1 = (bbox1[2] - bbox1[0]) * (bbox1[3] - bbox1[1])
        area2 = (bbox2[2] - bbox2[0]) * (bbox2[3] - bbox2[1])
        union_area = area1 + area2 - inter_area
        
        if union_area <= 0:
            return 1.0
        
        iou = inter_area / union_area
        return 1.0 - iou
    
    def _compute_pose_cost(self, pose_mu1, pose_sigma1, pose_mu2, pose_sigma2):
        """
        Compute pose similarity cost with uncertainty weighting.
        
        Uses uncertainty to down-weight unreliable keypoints in the distance computation.
        """
        # Convert to numpy if needed
        if torch.is_tensor(pose_mu1):
            pose_mu1 = pose_mu1.cpu().numpy()
        if torch.is_tensor(pose_sigma1):
            pose_sigma1 = pose_sigma1.cpu().numpy()
        if torch.is_tensor(pose_mu2):
            pose_mu2 = pose_mu2.cpu().numpy()
        if torch.is_tensor(pose_sigma2):
            pose_sigma2 = pose_sigma2.cpu().numpy()
        
        # Compute L2 distance between keypoints
        distances = np.linalg.norm(pose_mu1 - pose_mu2, axis=1)  # (num_joints,)
        
        # Compute uncertainty weights
        # Higher uncertainty -> lower weight in cost computation
        sigma_combined = np.mean([pose_sigma1, pose_sigma2], axis=0)  # (num_joints, 2)
        sigma_magnitude = np.linalg.norm(sigma_combined, axis=1)  # (num_joints,)
        
        # Weight inversely proportional to uncertainty
        # Add small epsilon to avoid division by zero
        weights = 1.0 / (sigma_magnitude + 1e-6)
        
        # Mask out very uncertain keypoints
        uncertain_mask = sigma_magnitude > self.uncertainty_threshold
        weights[uncertain_mask] = 0.1  # Give very small weight to uncertain keypoints
        
        # Normalize weights
        weights = weights / (np.sum(weights) + 1e-6)
        
        # Weighted average distance
        weighted_distance = np.sum(distances * weights)
        
        # Normalize by image size (assuming 256x192 input)
        normalized_distance = weighted_distance / 256.0
        
        return normalized_distance
    
    def _compute_mahalanobis_cost(self, pose_mu1, pose_sigma1, pose_mu2, pose_sigma2):
        """
        Alternative: Compute Mahalanobis distance using pose uncertainties.
        
        This treats pose_sigma as diagonal covariance matrix.
        """
        # Convert to numpy if needed
        if torch.is_tensor(pose_mu1):
            pose_mu1 = pose_mu1.cpu().numpy()
        if torch.is_tensor(pose_sigma1):
            pose_sigma1 = pose_sigma1.cpu().numpy()
        if torch.is_tensor(pose_mu2):
            pose_mu2 = pose_mu2.cpu().numpy()
        if torch.is_tensor(pose_sigma2):
            pose_sigma2 = pose_sigma2.cpu().numpy()
        
        # Flatten poses
        mu1_flat = pose_mu1.flatten()  # (num_joints * 2,)
        mu2_flat = pose_mu2.flatten()
        sigma1_flat = pose_sigma1.flatten()
        sigma2_flat = pose_sigma2.flatten()
        
        # Combined covariance (diagonal)
        combined_var = (sigma1_flat**2 + sigma2_flat**2) / 2
        combined_var = np.maximum(combined_var, 1e-6)  # Avoid numerical issues
        
        # Mahalanobis distance
        diff = mu1_flat - mu2_flat
        mahal_dist = np.sqrt(np.sum((diff**2) / combined_var))
        
        # Normalize
        normalized_dist = mahal_dist / len(mu1_flat)
        
        return normalized_dist
    
    def associate(self, tracks, detections):
        """
        Perform association using Hungarian algorithm.
        
        Args:
            tracks: List of track dictionaries
            detections: List of detection dictionaries
            
        Returns:
            matches: List of (track_idx, detection_idx) tuples
            unmatched_tracks: List of track indices
            unmatched_detections: List of detection indices
        """
        if len(tracks) == 0:
            return [], [], list(range(len(detections)))
        
        if len(detections) == 0:
            return [], list(range(len(tracks))), []
        
        # Compute cost matrix
        cost_matrix = self.compute_cost_matrix(tracks, detections)
        
        # Apply distance threshold
        cost_matrix[cost_matrix > self.max_distance] = self.max_distance + 1
        
        # Solve assignment problem
        track_indices, detection_indices = linear_sum_assignment(cost_matrix)
        
        # Filter out assignments with too high cost
        matches = []
        for t_idx, d_idx in zip(track_indices, detection_indices):
            if cost_matrix[t_idx, d_idx] <= self.max_distance:
                matches.append((t_idx, d_idx))
        
        # Find unmatched tracks and detections
        matched_track_indices = set([m[0] for m in matches])
        matched_detection_indices = set([m[1] for m in matches])
        
        unmatched_tracks = [i for i in range(len(tracks)) if i not in matched_track_indices]
        unmatched_detections = [i for i in range(len(detections)) if i not in matched_detection_indices]
        
        return matches, unmatched_tracks, unmatched_detections


class ConfidenceBasedAssociation(DistributionAwareAssociation):
    """Association that uses RLE confidence scores for weighting."""
    
    def __init__(self, confidence_threshold=0.5, **kwargs):
        super().__init__(**kwargs)
        self.confidence_threshold = confidence_threshold
    
    def _compute_pose_cost(self, pose_mu1, pose_sigma1, pose_mu2, pose_sigma2):
        """Compute pose cost using confidence scores derived from sigma."""
        # Convert to numpy if needed
        if torch.is_tensor(pose_mu1):
            pose_mu1 = pose_mu1.cpu().numpy()
        if torch.is_tensor(pose_sigma1):
            pose_sigma1 = pose_sigma1.cpu().numpy()
        if torch.is_tensor(pose_mu2):
            pose_mu2 = pose_mu2.cpu().numpy()
        if torch.is_tensor(pose_sigma2):
            pose_sigma2 = pose_sigma2.cpu().numpy()
        
        # Compute confidence scores (1 - sigma, as in RLE paper)
        conf1 = 1.0 - np.mean(pose_sigma1, axis=1)  # (num_joints,)
        conf2 = 1.0 - np.mean(pose_sigma2, axis=1)
        
        # Combined confidence
        combined_conf = (conf1 + conf2) / 2
        
        # Filter out low-confidence keypoints
        reliable_mask = combined_conf > self.confidence_threshold
        
        if not np.any(reliable_mask):
            # If no reliable keypoints, use all with equal weight
            reliable_mask = np.ones_like(combined_conf, dtype=bool)
            combined_conf = np.ones_like(combined_conf)
        
        # Compute distances only for reliable keypoints
        distances = np.linalg.norm(pose_mu1 - pose_mu2, axis=1)  # (num_joints,)
        
        # Weight by confidence
        weighted_distances = distances[reliable_mask] * combined_conf[reliable_mask]
        
        # Average weighted distance
        avg_distance = np.mean(weighted_distances) if len(weighted_distances) > 0 else self.max_distance
        
        # Normalize by image size
        normalized_distance = avg_distance / 256.0
        
        return normalized_distance
