"""
Motion models for tracking with uncertainty propagation
"""

import torch
import torch.nn as nn
import numpy as np
from filterpy.kalman import KalmanFilter


class KalmanMotionModel:
    """Kalman filter for tracking with pose uncertainty."""
    
    def __init__(self, num_joints=17, dt=1.0):
        """
        Initialize <PERSON><PERSON> filter for pose tracking.
        
        Args:
            num_joints: Number of keypoints
            dt: Time step between frames
        """
        self.num_joints = num_joints
        self.dt = dt
        
        # State: [x1, y1, vx1, vy1, x2, y2, vx2, vy2, ..., bbox_x, bbox_y, bbox_vx, bbox_vy, bbox_w, bbox_h]
        # Each joint has position (x, y) and velocity (vx, vy)
        # Plus bounding box center, velocity, and size
        state_dim = num_joints * 4 + 6  # 4 per joint + 6 for bbox
        obs_dim = num_joints * 2 + 4    # 2 per joint + 4 for bbox
        
        self.kf = KalmanFilter(dim_x=state_dim, dim_z=obs_dim)
        
        # State transition matrix (constant velocity model)
        self.kf.F = np.eye(state_dim)
        
        # Set velocity transitions for joints
        for i in range(num_joints):
            # x position influenced by x velocity
            self.kf.F[i*4, i*4+2] = dt
            # y position influenced by y velocity  
            self.kf.F[i*4+1, i*4+3] = dt
        
        # Set velocity transitions for bbox center
        bbox_start = num_joints * 4
        self.kf.F[bbox_start, bbox_start+2] = dt      # bbox_x influenced by bbox_vx
        self.kf.F[bbox_start+1, bbox_start+3] = dt    # bbox_y influenced by bbox_vy
        
        # Observation matrix (we observe positions only)
        self.kf.H = np.zeros((obs_dim, state_dim))
        
        # Joint observations
        for i in range(num_joints):
            self.kf.H[i*2, i*4] = 1.0      # observe x position
            self.kf.H[i*2+1, i*4+1] = 1.0  # observe y position
        
        # Bbox observations
        obs_bbox_start = num_joints * 2
        state_bbox_start = num_joints * 4
        self.kf.H[obs_bbox_start, state_bbox_start] = 1.0      # observe bbox_x
        self.kf.H[obs_bbox_start+1, state_bbox_start+1] = 1.0  # observe bbox_y
        self.kf.H[obs_bbox_start+2, state_bbox_start+4] = 1.0  # observe bbox_w
        self.kf.H[obs_bbox_start+3, state_bbox_start+5] = 1.0  # observe bbox_h
        
        # Process noise covariance
        self.kf.Q = np.eye(state_dim) * 0.1
        
        # Measurement noise covariance (will be updated based on RLE uncertainty)
        self.kf.R = np.eye(obs_dim) * 1.0
        
        # Initial state covariance
        self.kf.P = np.eye(state_dim) * 100.0
        
        self.initialized = False
    
    def initialize(self, detection_bbox, pose_mu, pose_sigma):
        """
        Initialize the Kalman filter with first detection.
        
        Args:
            detection_bbox: [x1, y1, x2, y2] bounding box
            pose_mu: (num_joints, 2) keypoint positions
            pose_sigma: (num_joints, 2) keypoint uncertainties
        """
        # Convert bbox to center, width, height
        bbox_x = (detection_bbox[0] + detection_bbox[2]) / 2
        bbox_y = (detection_bbox[1] + detection_bbox[3]) / 2
        bbox_w = detection_bbox[2] - detection_bbox[0]
        bbox_h = detection_bbox[3] - detection_bbox[1]
        
        # Initialize state
        state = np.zeros(self.kf.x.shape[0])
        
        # Set joint positions (velocities start at 0)
        for i in range(self.num_joints):
            state[i*4] = pose_mu[i, 0]      # x position
            state[i*4+1] = pose_mu[i, 1]    # y position
            # velocities remain 0
        
        # Set bbox state
        bbox_start = self.num_joints * 4
        state[bbox_start] = bbox_x
        state[bbox_start+1] = bbox_y
        state[bbox_start+4] = bbox_w
        state[bbox_start+5] = bbox_h
        
        self.kf.x = state
        
        # Set initial measurement noise based on pose uncertainty
        self._update_measurement_noise(pose_sigma)
        
        self.initialized = True
    
    def predict(self):
        """Predict next state."""
        if not self.initialized:
            raise RuntimeError("Kalman filter not initialized")
        
        self.kf.predict()
        
        # Extract predicted pose and bbox
        predicted_pose = np.zeros((self.num_joints, 2))
        for i in range(self.num_joints):
            predicted_pose[i, 0] = self.kf.x[i*4]      # x position
            predicted_pose[i, 1] = self.kf.x[i*4+1]    # y position
        
        # Extract predicted bbox
        bbox_start = self.num_joints * 4
        bbox_x = self.kf.x[bbox_start]
        bbox_y = self.kf.x[bbox_start+1]
        bbox_w = self.kf.x[bbox_start+4]
        bbox_h = self.kf.x[bbox_start+5]
        
        predicted_bbox = [
            bbox_x - bbox_w/2, bbox_y - bbox_h/2,
            bbox_x + bbox_w/2, bbox_y + bbox_h/2
        ]
        
        return predicted_pose, predicted_bbox
    
    def update(self, detection_bbox, pose_mu, pose_sigma):
        """
        Update with new observation.
        
        Args:
            detection_bbox: [x1, y1, x2, y2] bounding box
            pose_mu: (num_joints, 2) keypoint positions
            pose_sigma: (num_joints, 2) keypoint uncertainties
        """
        if not self.initialized:
            self.initialize(detection_bbox, pose_mu, pose_sigma)
            return
        
        # Update measurement noise based on current uncertainty
        self._update_measurement_noise(pose_sigma)
        
        # Prepare observation vector
        obs = np.zeros(self.kf.z.shape[0])
        
        # Joint observations
        for i in range(self.num_joints):
            obs[i*2] = pose_mu[i, 0]      # x position
            obs[i*2+1] = pose_mu[i, 1]    # y position
        
        # Bbox observations
        bbox_x = (detection_bbox[0] + detection_bbox[2]) / 2
        bbox_y = (detection_bbox[1] + detection_bbox[3]) / 2
        bbox_w = detection_bbox[2] - detection_bbox[0]
        bbox_h = detection_bbox[3] - detection_bbox[1]
        
        obs_bbox_start = self.num_joints * 2
        obs[obs_bbox_start] = bbox_x
        obs[obs_bbox_start+1] = bbox_y
        obs[obs_bbox_start+2] = bbox_w
        obs[obs_bbox_start+3] = bbox_h
        
        # Update Kalman filter
        self.kf.update(obs)
    
    def _update_measurement_noise(self, pose_sigma):
        """Update measurement noise covariance based on pose uncertainty."""
        # Joint measurement noise
        for i in range(self.num_joints):
            self.kf.R[i*2, i*2] = max(pose_sigma[i, 0], 1.0)      # x uncertainty
            self.kf.R[i*2+1, i*2+1] = max(pose_sigma[i, 1], 1.0)  # y uncertainty
        
        # Bbox measurement noise (fixed)
        obs_bbox_start = self.num_joints * 2
        self.kf.R[obs_bbox_start, obs_bbox_start] = 10.0      # bbox_x
        self.kf.R[obs_bbox_start+1, obs_bbox_start+1] = 10.0  # bbox_y
        self.kf.R[obs_bbox_start+2, obs_bbox_start+2] = 100.0 # bbox_w
        self.kf.R[obs_bbox_start+3, obs_bbox_start+3] = 100.0 # bbox_h
    
    def get_current_state(self):
        """Get current state estimate."""
        if not self.initialized:
            return None, None
        
        # Extract current pose
        current_pose = np.zeros((self.num_joints, 2))
        for i in range(self.num_joints):
            current_pose[i, 0] = self.kf.x[i*4]      # x position
            current_pose[i, 1] = self.kf.x[i*4+1]    # y position
        
        # Extract current bbox
        bbox_start = self.num_joints * 4
        bbox_x = self.kf.x[bbox_start]
        bbox_y = self.kf.x[bbox_start+1]
        bbox_w = self.kf.x[bbox_start+4]
        bbox_h = self.kf.x[bbox_start+5]
        
        current_bbox = [
            bbox_x - bbox_w/2, bbox_y - bbox_h/2,
            bbox_x + bbox_w/2, bbox_y + bbox_h/2
        ]
        
        return current_pose, current_bbox
    
    def get_uncertainty(self):
        """Get current state uncertainty."""
        if not self.initialized:
            return None
        
        # Extract pose uncertainty from covariance matrix
        pose_uncertainty = np.zeros((self.num_joints, 2))
        for i in range(self.num_joints):
            pose_uncertainty[i, 0] = np.sqrt(self.kf.P[i*4, i*4])      # x uncertainty
            pose_uncertainty[i, 1] = np.sqrt(self.kf.P[i*4+1, i*4+1])  # y uncertainty
        
        return pose_uncertainty


class SimpleMotionModel:
    """Simple constant velocity motion model without Kalman filtering."""
    
    def __init__(self, num_joints=17):
        self.num_joints = num_joints
        self.prev_pose = None
        self.prev_bbox = None
        self.velocity_pose = None
        self.velocity_bbox = None
        self.initialized = False
    
    def initialize(self, detection_bbox, pose_mu, pose_sigma):
        """Initialize with first detection."""
        self.prev_pose = pose_mu.copy()
        self.prev_bbox = np.array(detection_bbox)
        self.velocity_pose = np.zeros_like(pose_mu)
        self.velocity_bbox = np.zeros(4)
        self.initialized = True
    
    def predict(self):
        """Predict next state using constant velocity."""
        if not self.initialized:
            raise RuntimeError("Motion model not initialized")
        
        predicted_pose = self.prev_pose + self.velocity_pose
        predicted_bbox = self.prev_bbox + self.velocity_bbox
        
        return predicted_pose, predicted_bbox
    
    def update(self, detection_bbox, pose_mu, pose_sigma):
        """Update with new observation."""
        if not self.initialized:
            self.initialize(detection_bbox, pose_mu, pose_sigma)
            return
        
        # Update velocities
        self.velocity_pose = pose_mu - self.prev_pose
        self.velocity_bbox = np.array(detection_bbox) - self.prev_bbox
        
        # Update previous state
        self.prev_pose = pose_mu.copy()
        self.prev_bbox = np.array(detection_bbox)
    
    def get_current_state(self):
        """Get current state estimate."""
        if not self.initialized:
            return None, None
        return self.prev_pose, self.prev_bbox
