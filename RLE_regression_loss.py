import torch
import torch.nn as nn
import torch.nn.functional as F
import math

from builder import LOSS

@LOSS.register_module
class RLELoss(nn.Module):
    """
    Improved RLE Regression Loss with proper uncertainty modeling

    This loss combines:
    1. Normalizing Flow loss for distribution modeling
    2. Residual log-likelihood estimation for uncertainty quantification
    3. Adaptive weighting based on keypoint visibility
    """

    def __init__(self, use_target_weight=True, loss_weight=1.0,
                 residual_weight=1.0, nf_weight=1.0, size_average=True):
        super(RLELoss, self).__init__()
        self.use_target_weight = use_target_weight
        self.loss_weight = loss_weight
        self.residual_weight = residual_weight
        self.nf_weight = nf_weight
        self.size_average = size_average

        # Constants for RLE
        self.log_2pi = math.log(2 * math.pi)
        self.sqrt_2pi = math.sqrt(2 * math.pi)
        self.eps = 1e-6

    def logQ(self, gt_uv, pred_jts, sigma):
        """
        Compute the log probability under Laplace distribution

        Args:
            gt_uv: Ground truth coordinates
            pred_jts: Predicted coordinates
            sigma: Predicted uncertainties

        Returns:
            Log probability
        """
        # Laplace distribution: -log(2σ) - |x-μ|/σ
        residual = torch.abs(gt_uv - pred_jts)
        log_prob = torch.log(2 * sigma + self.eps) + residual / (sigma + self.eps)
        return log_prob

    def forward(self, output, labels):
        """
        Compute RLE loss with improved handling

        Args:
            output: Model output with pred_jts, sigma, nf_loss
            labels: Ground truth labels with coordinates and weights

        Returns:
            Total RLE loss
        """
        # Extract predictions
        pred_jts = output.pred_jts
        sigma = output.sigma
        nf_loss = output.nf_loss

        # Extract ground truth - handle different label formats
        if isinstance(labels, dict):
            if 'target_uv' in labels:
                gt_uv = labels['target_uv']
                gt_uv_weight = labels.get('target_uv_weight', torch.ones_like(gt_uv))
            elif 'poses' in labels:
                gt_uv = labels['poses']
                gt_uv_weight = labels.get('pose_weights', torch.ones_like(gt_uv))
            else:
                raise ValueError("Labels must contain 'target_uv' or 'poses'")
        else:
            gt_uv = labels
            gt_uv_weight = torch.ones_like(labels)

        # Ensure consistent shapes
        if gt_uv.shape != pred_jts.shape:
            gt_uv = gt_uv.reshape(pred_jts.shape)
        if gt_uv_weight.shape != pred_jts.shape:
            gt_uv_weight = gt_uv_weight.reshape(pred_jts.shape)

        # Compute losses
        total_loss = 0.0

        # 1. Normalizing Flow loss (if available)
        if nf_loss is not None and self.nf_weight > 0:
            if self.use_target_weight:
                # Apply weights to NF loss
                if nf_loss.dim() == 3:  # (B, N, 2)
                    weighted_nf_loss = nf_loss * gt_uv_weight
                else:  # (B, N, 1) or (B, N)
                    weight_mean = gt_uv_weight.mean(dim=-1, keepdim=True)
                    weighted_nf_loss = nf_loss * weight_mean
            else:
                weighted_nf_loss = nf_loss

            nf_loss_value = weighted_nf_loss.sum() / max(gt_uv_weight.sum(), 1.0) if self.size_average else weighted_nf_loss.sum()
            total_loss += self.nf_weight * nf_loss_value

        # 2. Residual log-likelihood loss
        if self.residual_weight > 0:
            Q_logprob = self.logQ(gt_uv, pred_jts, sigma)

            if self.use_target_weight:
                Q_logprob = Q_logprob * gt_uv_weight

            residual_loss = Q_logprob.sum() / max(gt_uv_weight.sum(), 1.0) if self.size_average else Q_logprob.sum()
            total_loss += self.residual_weight * residual_loss

        return self.loss_weight * total_loss


@LOSS.register_module
class AdaptiveRLELoss(RLELoss):
    """
    Adaptive RLE Loss that adjusts weights based on uncertainty levels
    """

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.uncertainty_threshold = 0.1

    def forward(self, output, labels):
        """Forward with adaptive weighting based on uncertainty"""
        # Get base loss
        base_loss = super().forward(output, labels)

        # Adaptive weighting based on uncertainty
        sigma = output.sigma
        avg_uncertainty = sigma.mean()

        # Increase residual weight for high uncertainty cases
        if avg_uncertainty > self.uncertainty_threshold:
            uncertainty_factor = torch.clamp(avg_uncertainty / self.uncertainty_threshold, 1.0, 3.0)
            base_loss = base_loss * uncertainty_factor

        return base_loss