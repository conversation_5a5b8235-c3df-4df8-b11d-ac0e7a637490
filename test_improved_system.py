"""
Test script for the improved MPPET-RLE system
"""

import torch
import numpy as np
import sys
import os
import traceback

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_dataset():
    """Test the improved dataset loader"""
    print("=" * 50)
    print("Testing Improved Dataset Loader...")
    
    try:
        from datasets.posetrack_dataset import PoseTrackDataset, get_transform, collate_fn
        from torch.utils.data import DataLoader
        
        # Create dataset with dummy data
        transform = get_transform('train')
        dataset = PoseTrackDataset(
            data_root='dummy_path',  # Will create dummy data
            split='train',
            transform=transform,
            sequence_length=1,
            input_size=(192, 256),
            normalize_coords=True
        )
        
        print(f"✓ Dataset created with {len(dataset)} samples")
        
        # Test data loading
        dataloader = DataLoader(
            dataset,
            batch_size=2,
            shuffle=False,
            collate_fn=collate_fn,
            num_workers=0  # Avoid multiprocessing issues in test
        )
        
        # Test one batch
        for batch_data in dataloader:
            images = batch_data['images']
            poses = batch_data['poses']
            pose_weights = batch_data['pose_weights']
            
            print(f"✓ Batch loaded successfully:")
            print(f"  - Images shape: {images.shape}")
            print(f"  - Poses shape: {poses.shape}")
            print(f"  - Pose weights shape: {pose_weights.shape}")

            if poses.numel() > 0:
                print(f"  - Coordinate range: [{poses.min():.3f}, {poses.max():.3f}]")
            else:
                print(f"  - No valid poses in batch (empty tensor)")
            break
        
        return True
        
    except Exception as e:
        print(f"✗ Dataset test failed: {str(e)}")
        traceback.print_exc()
        return False


def test_improved_rle_model():
    """Test the improved RLE pose estimation model"""
    print("=" * 50)
    print("Testing Improved RLE Model...")
    
    try:
        from simple_rle_pose import SimpleRegressFlow
        
        # Create model with improved configuration
        model = SimpleRegressFlow(
            num_joints=17,
            input_size=(192, 256),
            normalize_coords=True,
            backbone='resnet50',
            pretrained=False  # Faster for testing
        )
        model.eval()
        
        print(f"✓ Model created successfully")
        
        # Test forward pass
        batch_size = 2
        input_tensor = torch.randn(batch_size, 3, 256, 192)  # (B, C, H, W)
        
        with torch.no_grad():
            output = model(input_tensor)
        
        print(f"✓ Forward pass successful:")
        print(f"  - Predicted joints shape: {output.pred_jts.shape}")
        print(f"  - Sigma shape: {output.sigma.shape}")
        print(f"  - Confidence shape: {output.maxvals.shape}")
        print(f"  - Coordinate range: [{output.pred_jts.min():.3f}, {output.pred_jts.max():.3f}]")
        print(f"  - Sigma range: [{output.sigma.min():.3f}, {output.sigma.max():.3f}]")
        
        # Test with labels (training mode)
        model.train()
        labels = {
            'poses': torch.randn(batch_size, 17, 2),
            'pose_weights': torch.ones(batch_size, 17, 2)
        }
        
        output_train = model(input_tensor, labels)
        print(f"✓ Training forward pass successful")
        print(f"  - NF loss computed: {output_train.nf_loss is not None}")
        
        return True
        
    except Exception as e:
        print(f"✗ RLE model test failed: {str(e)}")
        traceback.print_exc()
        return False


def test_improved_loss():
    """Test the improved RLE loss function"""
    print("=" * 50)
    print("Testing Improved RLE Loss...")
    
    try:
        from RLE_regression_loss import RLELoss, AdaptiveRLELoss
        from easydict import EasyDict
        
        # Create loss functions
        rle_loss = RLELoss(use_target_weight=True, size_average=True)
        adaptive_loss = AdaptiveRLELoss(use_target_weight=True, size_average=True)
        
        print(f"✓ Loss functions created")
        
        # Create dummy data
        batch_size = 2
        num_joints = 17
        
        # Model output (with requires_grad=True for testing backward pass)
        output = EasyDict(
            pred_jts=torch.randn(batch_size, num_joints, 2, requires_grad=True),
            sigma=torch.abs(torch.randn(batch_size, num_joints, 2, requires_grad=True)) + 0.1,
            nf_loss=torch.randn(batch_size, num_joints, 2, requires_grad=True)
        )

        # Labels
        labels = {
            'poses': torch.randn(batch_size, num_joints, 2),
            'pose_weights': torch.ones(batch_size, num_joints, 2)
        }
        
        # Test RLE loss
        loss_value = rle_loss(output, labels)
        print(f"✓ RLE Loss computed: {loss_value.item():.4f}")
        
        # Test adaptive loss
        adaptive_loss_value = adaptive_loss(output, labels)
        print(f"✓ Adaptive RLE Loss computed: {adaptive_loss_value.item():.4f}")
        
        # Test backward pass
        loss_value.backward()
        print(f"✓ Backward pass successful")
        
        return True
        
    except Exception as e:
        print(f"✗ Loss function test failed: {str(e)}")
        traceback.print_exc()
        return False


def test_full_training_step():
    """Test a complete training step"""
    print("=" * 50)
    print("Testing Full Training Step...")
    
    try:
        from datasets.posetrack_dataset import PoseTrackDataset, get_transform, collate_fn
        from simple_rle_pose import SimpleRegressFlow
        from RLE_regression_loss import AdaptiveRLELoss
        from torch.utils.data import DataLoader
        import torch.optim as optim
        
        # Create dataset
        transform = get_transform('train')
        dataset = PoseTrackDataset(
            data_root='dummy_path',
            split='train',
            transform=transform,
            sequence_length=1,
            input_size=(192, 256),
            normalize_coords=True
        )
        
        dataloader = DataLoader(
            dataset,
            batch_size=2,
            shuffle=False,
            collate_fn=collate_fn,
            num_workers=0
        )
        
        # Create model
        model = SimpleRegressFlow(
            num_joints=17,
            input_size=(192, 256),
            normalize_coords=True,
            backbone='resnet50',
            pretrained=False
        )
        
        # Create loss and optimizer
        criterion = AdaptiveRLELoss(use_target_weight=True, size_average=True)
        optimizer = optim.Adam(model.parameters(), lr=1e-4)
        
        print(f"✓ Training components created")
        
        # Training step
        model.train()
        for batch_data in dataloader:
            images = batch_data['images']
            poses = batch_data['poses']
            pose_weights = batch_data['pose_weights']

            # Skip empty batches
            if len(images) == 0:
                print("⚠️  Skipping empty batch")
                continue

            labels = {
                'poses': poses,
                'pose_weights': pose_weights
            }

            optimizer.zero_grad()

            # Forward pass
            outputs = model(images, labels)

            # Compute loss
            loss = criterion(outputs, labels)

            # Backward pass
            loss.backward()
            optimizer.step()

            print(f"✓ Training step completed:")
            print(f"  - Loss: {loss.item():.4f}")
            print(f"  - Batch size: {len(images)}")
            break
        else:
            print("⚠️  No valid batches found for training")
        
        return True
        
    except Exception as e:
        print(f"✗ Full training step test failed: {str(e)}")
        traceback.print_exc()
        return False


def main():
    """Run all tests"""
    print("🔍 Testing Improved MPPET-RLE System")
    print("=" * 60)
    
    tests = [
        ("Dataset Loader", test_dataset),
        ("RLE Model", test_improved_rle_model),
        ("Loss Functions", test_improved_loss),
        ("Full Training Step", test_full_training_step)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} test...")
        success = test_func()
        results.append((test_name, success))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name:<20}: {status}")
        if success:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All tests passed! The improved system is ready for training.")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")


if __name__ == '__main__':
    main()
