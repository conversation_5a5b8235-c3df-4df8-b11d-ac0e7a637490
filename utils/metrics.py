"""
Evaluation metrics for multi-person pose tracking
"""

import numpy as np
import torch
from collections import defaultdict
import motmetrics as mm


class PoseTrackingMetrics:
    """Compute tracking metrics for pose estimation and tracking."""
    
    def __init__(self, num_joints=17, oks_threshold=0.5):
        """
        Initialize metrics calculator.
        
        Args:
            num_joints: Number of keypoints
            oks_threshold: OKS threshold for pose matching
        """
        self.num_joints = num_joints
        self.oks_threshold = oks_threshold
        
        # MOT metrics accumulator
        self.mot_accumulator = mm.MOTAccumulator(auto_id=False)
        
        # Pose metrics
        self.pose_metrics = {
            'total_poses': 0,
            'correct_poses': 0,
            'total_keypoints': 0,
            'correct_keypoints': 0,
            'oks_scores': []
        }
        
        # Tracking metrics
        self.tracking_metrics = {
            'frame_count': 0,
            'total_tracks': 0,
            'id_switches': 0,
            'fragmentations': 0
        }
        
        # OKS sigmas for different joints (from COCO)
        self.oks_sigmas = np.array([
            0.026, 0.025, 0.025, 0.035, 0.035,  # head keypoints
            0.079, 0.079, 0.072, 0.072,        # shoulder, elbow
            0.062, 0.062, 0.107, 0.107,        # wrist, hip
            0.087, 0.087, 0.089, 0.089         # knee, ankle
        ])
    
    def compute_oks(self, gt_pose, pred_pose, bbox_area):
        """
        Compute Object Keypoint Similarity (OKS).
        
        Args:
            gt_pose: Ground truth pose (num_joints, 2)
            pred_pose: Predicted pose (num_joints, 2)
            bbox_area: Area of bounding box for normalization
            
        Returns:
            OKS score
        """
        if torch.is_tensor(gt_pose):
            gt_pose = gt_pose.cpu().numpy()
        if torch.is_tensor(pred_pose):
            pred_pose = pred_pose.cpu().numpy()
        
        # Compute squared distances
        distances = np.linalg.norm(gt_pose - pred_pose, axis=1) ** 2
        
        # Normalize by bbox area and joint-specific sigmas
        normalized_distances = distances / (2 * self.oks_sigmas ** 2 * bbox_area)
        
        # Compute OKS
        oks = np.exp(-normalized_distances)
        
        # Average over visible keypoints
        return np.mean(oks)
    
    def update_pose_metrics(self, gt_poses, pred_poses, gt_bboxes, pred_bboxes):
        """
        Update pose estimation metrics.
        
        Args:
            gt_poses: List of ground truth poses
            pred_poses: List of predicted poses
            gt_bboxes: List of ground truth bounding boxes
            pred_bboxes: List of predicted bounding boxes
        """
        # Match predictions to ground truth based on IoU
        matches = self._match_poses_by_iou(gt_bboxes, pred_bboxes)
        
        for gt_idx, pred_idx in matches:
            gt_pose = gt_poses[gt_idx]
            pred_pose = pred_poses[pred_idx]
            gt_bbox = gt_bboxes[gt_idx]
            
            # Compute bbox area
            if torch.is_tensor(gt_bbox):
                gt_bbox = gt_bbox.cpu().numpy()
            bbox_area = (gt_bbox[2] - gt_bbox[0]) * (gt_bbox[3] - gt_bbox[1])
            
            # Compute OKS
            oks = self.compute_oks(gt_pose, pred_pose, bbox_area)
            self.pose_metrics['oks_scores'].append(oks)
            
            # Count correct poses
            if oks > self.oks_threshold:
                self.pose_metrics['correct_poses'] += 1
            
            # Count correct keypoints
            if torch.is_tensor(gt_pose):
                gt_pose = gt_pose.cpu().numpy()
            if torch.is_tensor(pred_pose):
                pred_pose = pred_pose.cpu().numpy()
            
            keypoint_distances = np.linalg.norm(gt_pose - pred_pose, axis=1)
            correct_keypoints = np.sum(keypoint_distances < 10.0)  # 10 pixel threshold
            
            self.pose_metrics['correct_keypoints'] += correct_keypoints
            self.pose_metrics['total_keypoints'] += len(gt_pose)
        
        self.pose_metrics['total_poses'] += len(gt_poses)
    
    def update_tracking_metrics(self, gt_tracks, pred_tracks, frame_id):
        """
        Update tracking metrics using MOTMetrics.
        
        Args:
            gt_tracks: List of ground truth tracks with 'track_id' and 'bbox'
            pred_tracks: List of predicted tracks with 'track_id' and 'bbox'
            frame_id: Current frame ID
        """
        self.tracking_metrics['frame_count'] += 1
        
        # Extract track IDs and bounding boxes
        gt_ids = [track['track_id'] for track in gt_tracks]
        pred_ids = [track['track_id'] for track in pred_tracks]
        
        gt_bboxes = [track['bbox'] for track in gt_tracks]
        pred_bboxes = [track['bbox'] for track in pred_tracks]
        
        # Compute distance matrix (using IoU distance)
        if len(gt_bboxes) > 0 and len(pred_bboxes) > 0:
            distance_matrix = self._compute_iou_distance_matrix(gt_bboxes, pred_bboxes)
        else:
            distance_matrix = np.array([]).reshape(len(gt_bboxes), len(pred_bboxes))
        
        # Update MOT accumulator
        self.mot_accumulator.update(
            gt_ids, pred_ids, distance_matrix, frameid=frame_id
        )
    
    def _match_poses_by_iou(self, gt_bboxes, pred_bboxes, iou_threshold=0.5):
        """Match poses based on bounding box IoU."""
        if len(gt_bboxes) == 0 or len(pred_bboxes) == 0:
            return []
        
        # Compute IoU matrix
        iou_matrix = np.zeros((len(gt_bboxes), len(pred_bboxes)))
        
        for i, gt_bbox in enumerate(gt_bboxes):
            for j, pred_bbox in enumerate(pred_bboxes):
                iou = self._compute_iou(gt_bbox, pred_bbox)
                iou_matrix[i, j] = iou
        
        # Find matches using greedy assignment
        matches = []
        used_pred = set()
        
        for i in range(len(gt_bboxes)):
            best_j = -1
            best_iou = iou_threshold
            
            for j in range(len(pred_bboxes)):
                if j not in used_pred and iou_matrix[i, j] > best_iou:
                    best_iou = iou_matrix[i, j]
                    best_j = j
            
            if best_j != -1:
                matches.append((i, best_j))
                used_pred.add(best_j)
        
        return matches
    
    def _compute_iou(self, bbox1, bbox2):
        """Compute IoU between two bounding boxes."""
        if torch.is_tensor(bbox1):
            bbox1 = bbox1.cpu().numpy()
        if torch.is_tensor(bbox2):
            bbox2 = bbox2.cpu().numpy()
        
        x1_inter = max(bbox1[0], bbox2[0])
        y1_inter = max(bbox1[1], bbox2[1])
        x2_inter = min(bbox1[2], bbox2[2])
        y2_inter = min(bbox1[3], bbox2[3])
        
        if x2_inter <= x1_inter or y2_inter <= y1_inter:
            return 0.0
        
        inter_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)
        area1 = (bbox1[2] - bbox1[0]) * (bbox1[3] - bbox1[1])
        area2 = (bbox2[2] - bbox2[0]) * (bbox2[3] - bbox2[1])
        union_area = area1 + area2 - inter_area
        
        return inter_area / union_area if union_area > 0 else 0.0
    
    def _compute_iou_distance_matrix(self, gt_bboxes, pred_bboxes):
        """Compute IoU distance matrix (1 - IoU)."""
        distance_matrix = np.ones((len(gt_bboxes), len(pred_bboxes)))
        
        for i, gt_bbox in enumerate(gt_bboxes):
            for j, pred_bbox in enumerate(pred_bboxes):
                iou = self._compute_iou(gt_bbox, pred_bbox)
                distance_matrix[i, j] = 1.0 - iou
        
        return distance_matrix
    
    def get_pose_metrics(self):
        """Get pose estimation metrics."""
        if self.pose_metrics['total_poses'] == 0:
            return {'AP_pose': 0.0, 'PCK': 0.0, 'mean_OKS': 0.0}
        
        # Average Precision for pose
        ap_pose = self.pose_metrics['correct_poses'] / self.pose_metrics['total_poses']
        
        # Percentage of Correct Keypoints
        if self.pose_metrics['total_keypoints'] > 0:
            pck = self.pose_metrics['correct_keypoints'] / self.pose_metrics['total_keypoints']
        else:
            pck = 0.0
        
        # Mean OKS
        if len(self.pose_metrics['oks_scores']) > 0:
            mean_oks = np.mean(self.pose_metrics['oks_scores'])
        else:
            mean_oks = 0.0
        
        return {
            'AP_pose': ap_pose,
            'PCK': pck,
            'mean_OKS': mean_oks
        }
    
    def get_tracking_metrics(self):
        """Get tracking metrics."""
        # Compute MOT metrics
        mh = mm.metrics.create()
        summary = mh.compute(self.mot_accumulator, metrics=['mota', 'motp', 'num_switches', 'num_fragmentations'], name='acc')
        
        if len(summary) > 0:
            mota = summary['mota'].iloc[0] if not np.isnan(summary['mota'].iloc[0]) else 0.0
            motp = summary['motp'].iloc[0] if not np.isnan(summary['motp'].iloc[0]) else 0.0
            num_switches = summary['num_switches'].iloc[0] if not np.isnan(summary['num_switches'].iloc[0]) else 0
            num_fragmentations = summary['num_fragmentations'].iloc[0] if not np.isnan(summary['num_fragmentations'].iloc[0]) else 0
        else:
            mota = motp = num_switches = num_fragmentations = 0
        
        return {
            'MOTA': mota,
            'MOTP': motp,
            'ID_switches': int(num_switches),
            'Fragmentations': int(num_fragmentations),
            'Total_frames': self.tracking_metrics['frame_count']
        }
    
    def get_all_metrics(self):
        """Get all metrics."""
        pose_metrics = self.get_pose_metrics()
        tracking_metrics = self.get_tracking_metrics()
        
        return {
            **pose_metrics,
            **tracking_metrics
        }
    
    def reset(self):
        """Reset all metrics."""
        self.mot_accumulator = mm.MOTAccumulator(auto_id=True)
        
        self.pose_metrics = {
            'total_poses': 0,
            'correct_poses': 0,
            'total_keypoints': 0,
            'correct_keypoints': 0,
            'oks_scores': []
        }
        
        self.tracking_metrics = {
            'frame_count': 0,
            'total_tracks': 0,
            'id_switches': 0,
            'fragmentations': 0
        }


def compute_posetrack_metrics(gt_data, pred_data):
    """
    Compute PoseTrack21 evaluation metrics.
    
    Args:
        gt_data: Ground truth data
        pred_data: Prediction data
        
    Returns:
        Dictionary with evaluation metrics
    """
    metrics_calculator = PoseTrackingMetrics()
    
    # Process each frame
    for frame_data in zip(gt_data, pred_data):
        gt_frame, pred_frame = frame_data
        
        # Extract poses and tracks
        gt_poses = [ann['keypoints'] for ann in gt_frame['annotations']]
        gt_bboxes = [ann['bbox'] for ann in gt_frame['annotations']]
        gt_tracks = [{'track_id': ann.get('track_id', 0), 'bbox': ann['bbox']} 
                    for ann in gt_frame['annotations']]
        
        pred_poses = [det['pose'] for det in pred_frame['detections']]
        pred_bboxes = [det['bbox'] for det in pred_frame['detections']]
        pred_tracks = [{'track_id': det.get('track_id', 0), 'bbox': det['bbox']} 
                      for det in pred_frame['detections']]
        
        # Update metrics
        metrics_calculator.update_pose_metrics(gt_poses, pred_poses, gt_bboxes, pred_bboxes)
        metrics_calculator.update_tracking_metrics(gt_tracks, pred_tracks, gt_frame['frame_id'])
    
    return metrics_calculator.get_all_metrics()
