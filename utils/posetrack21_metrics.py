"""
PoseTrack21 evaluation metrics implementation
"""

import numpy as np
import torch
from collections import defaultdict
import json
import os


class PoseTrack21Evaluator:
    """
    PoseTrack21 evaluation following official evaluation protocol
    
    Metrics:
    - PCK (Percentage of Correct Keypoints) for pose estimation
    - MOTA (Multiple Object Tracking Accuracy) for tracking
    - MOTP (Multiple Object Tracking Precision) for tracking
    """
    
    def __init__(self, num_joints=17, pck_threshold=0.5, oks_threshold=0.5):
        self.num_joints = num_joints
        self.pck_threshold = pck_threshold
        self.oks_threshold = oks_threshold
        
        # PoseTrack21 joint names
        self.joint_names = [
            "nose", "head_bottom", "head_top", "left_ear", "right_ear",
            "left_shoulder", "right_shoulder", "left_elbow", "right_elbow", 
            "left_wrist", "right_wrist", "left_hip", "right_hip",
            "left_knee", "right_knee", "left_ankle", "right_ankle"
        ]
        
        # OKS sigmas for each joint (from COCO)
        self.oks_sigmas = np.array([
            0.026, 0.025, 0.025, 0.035, 0.035, 0.079, 0.079, 0.072, 0.072,
            0.062, 0.062, 0.107, 0.107, 0.087, 0.087, 0.089, 0.089
        ])
        
        # Reset metrics
        self.reset()
    
    def reset(self):
        """Reset all metrics"""
        self.pose_results = []
        self.tracking_results = []
        self.gt_poses = []
        self.gt_tracks = []
        
    def add_batch(self, predictions, ground_truth, frame_info):
        """
        Add a batch of predictions and ground truth
        
        Args:
            predictions: Dict with 'poses', 'scores', 'track_ids'
            ground_truth: Dict with 'poses', 'track_ids', 'visibility'
            frame_info: Dict with 'vid_id', 'frame_id', 'image_size'
        """
        batch_size = len(frame_info)
        
        for i in range(batch_size):
            frame_data = frame_info[i]
            
            # Extract predictions for this frame
            if i < len(predictions['poses']):
                pred_poses = predictions['poses'][i]  # (N, 17, 2)
                pred_scores = predictions.get('scores', [1.0] * len(pred_poses))
                pred_track_ids = predictions.get('track_ids', list(range(len(pred_poses))))
            else:
                pred_poses = []
                pred_scores = []
                pred_track_ids = []
            
            # Extract ground truth for this frame
            if i < len(ground_truth['poses']):
                gt_poses = ground_truth['poses'][i]  # (M, 17, 2)
                gt_track_ids = ground_truth.get('track_ids', list(range(len(gt_poses))))
                gt_visibility = ground_truth.get('visibility', np.ones((len(gt_poses), 17)))
            else:
                gt_poses = []
                gt_track_ids = []
                gt_visibility = []
            
            # Store results
            self.pose_results.append({
                'vid_id': frame_data['vid_id'],
                'frame_id': frame_data['frame_id'],
                'pred_poses': pred_poses,
                'pred_scores': pred_scores,
                'pred_track_ids': pred_track_ids,
                'image_size': frame_data.get('image_size', (640, 480))
            })
            
            self.gt_poses.append({
                'vid_id': frame_data['vid_id'],
                'frame_id': frame_data['frame_id'],
                'gt_poses': gt_poses,
                'gt_track_ids': gt_track_ids,
                'gt_visibility': gt_visibility,
                'image_size': frame_data.get('image_size', (640, 480))
            })
    
    def compute_pck(self):
        """Compute PCK (Percentage of Correct Keypoints)"""
        if not self.pose_results:
            return {'PCK': 0.0, 'PCK_per_joint': [0.0] * self.num_joints}
        
        total_keypoints = 0
        correct_keypoints = 0
        correct_per_joint = [0] * self.num_joints
        total_per_joint = [0] * self.num_joints
        
        for pred_data, gt_data in zip(self.pose_results, self.gt_poses):
            if len(pred_data['pred_poses']) == 0 or len(gt_data['gt_poses']) == 0:
                continue
            
            pred_poses = np.array(pred_data['pred_poses'])
            gt_poses = np.array(gt_data['gt_poses'])
            gt_visibility = np.array(gt_data['gt_visibility'])
            
            # Match predictions to ground truth (simple nearest neighbor)
            matches = self._match_poses(pred_poses, gt_poses)
            
            for pred_idx, gt_idx in matches:
                if gt_idx >= len(gt_poses):
                    continue
                    
                pred_pose = pred_poses[pred_idx]
                gt_pose = gt_poses[gt_idx]
                visibility = gt_visibility[gt_idx]
                
                # Compute head size for normalization
                head_size = self._compute_head_size(gt_pose)
                
                for joint_idx in range(self.num_joints):
                    # Handle different visibility array sizes
                    if joint_idx < len(visibility):
                        joint_visible = visibility[joint_idx]
                        if hasattr(joint_visible, '__len__'):
                            # If it's an array, check if any element is > 0
                            is_visible = np.any(joint_visible > 0)
                        else:
                            # If it's a scalar, check directly
                            is_visible = joint_visible > 0
                    else:
                        # If joint_idx is out of bounds, assume not visible
                        is_visible = False

                    if is_visible:  # Only evaluate visible joints
                        pred_joint = pred_pose[joint_idx]
                        gt_joint = gt_pose[joint_idx]

                        # Compute distance
                        distance = np.linalg.norm(pred_joint - gt_joint)

                        # Check if correct (within threshold * head_size)
                        if distance <= self.pck_threshold * head_size:
                            correct_keypoints += 1
                            correct_per_joint[joint_idx] += 1

                        total_keypoints += 1
                        total_per_joint[joint_idx] += 1
        
        # Compute PCK
        pck = correct_keypoints / max(total_keypoints, 1)
        pck_per_joint = [
            correct_per_joint[i] / max(total_per_joint[i], 1) 
            for i in range(self.num_joints)
        ]
        
        return {
            'PCK': pck,
            'PCK_per_joint': pck_per_joint,
            'joint_names': self.joint_names
        }
    
    def compute_oks(self):
        """Compute OKS (Object Keypoint Similarity)"""
        if not self.pose_results:
            return {'mOKS': 0.0}
        
        total_oks = 0
        num_matches = 0
        
        for pred_data, gt_data in zip(self.pose_results, self.gt_poses):
            if len(pred_data['pred_poses']) == 0 or len(gt_data['gt_poses']) == 0:
                continue
            
            pred_poses = np.array(pred_data['pred_poses'])
            gt_poses = np.array(gt_data['gt_poses'])
            gt_visibility = np.array(gt_data['gt_visibility'])
            
            # Match predictions to ground truth
            matches = self._match_poses(pred_poses, gt_poses)
            
            for pred_idx, gt_idx in matches:
                if gt_idx >= len(gt_poses):
                    continue
                    
                pred_pose = pred_poses[pred_idx]
                gt_pose = gt_poses[gt_idx]
                visibility = gt_visibility[gt_idx]
                
                # Compute OKS
                oks = self._compute_oks(pred_pose, gt_pose, visibility)
                total_oks += oks
                num_matches += 1
        
        moks = total_oks / max(num_matches, 1)
        return {'mOKS': moks}
    
    def _match_poses(self, pred_poses, gt_poses):
        """Simple nearest neighbor matching"""
        if len(pred_poses) == 0 or len(gt_poses) == 0:
            return []
        
        matches = []
        used_gt = set()
        
        for pred_idx, pred_pose in enumerate(pred_poses):
            best_gt_idx = -1
            best_distance = float('inf')
            
            for gt_idx, gt_pose in enumerate(gt_poses):
                if gt_idx in used_gt:
                    continue
                
                # Compute center distance
                pred_center = np.mean(pred_pose, axis=0)
                gt_center = np.mean(gt_pose, axis=0)
                distance = np.linalg.norm(pred_center - gt_center)
                
                if distance < best_distance:
                    best_distance = distance
                    best_gt_idx = gt_idx
            
            if best_gt_idx != -1:
                matches.append((pred_idx, best_gt_idx))
                used_gt.add(best_gt_idx)
        
        return matches
    
    def _compute_head_size(self, pose):
        """Compute head size for PCK normalization"""
        # Use distance between head_top and head_bottom
        head_top = pose[2]  # head_top
        head_bottom = pose[1]  # head_bottom
        head_size = np.linalg.norm(head_top - head_bottom)
        
        # Fallback to torso size if head size is too small
        if head_size < 10:
            left_shoulder = pose[5]
            right_shoulder = pose[6]
            left_hip = pose[11]
            right_hip = pose[12]
            
            shoulder_dist = np.linalg.norm(left_shoulder - right_shoulder)
            hip_dist = np.linalg.norm(left_hip - right_hip)
            torso_height = np.linalg.norm((left_shoulder + right_shoulder) / 2 - (left_hip + right_hip) / 2)
            
            head_size = max(shoulder_dist, hip_dist, torso_height) * 0.6
        
        return max(head_size, 20)  # Minimum head size
    
    def _compute_oks(self, pred_pose, gt_pose, visibility):
        """Compute Object Keypoint Similarity"""
        # Handle different visibility array sizes
        visibility = np.array(visibility).flatten()  # Ensure it's a 1D array

        if len(visibility) < self.num_joints:
            # Pad visibility array if it's shorter
            padded_visibility = np.zeros(self.num_joints)
            padded_visibility[:len(visibility)] = visibility
            visibility = padded_visibility
        elif len(visibility) > self.num_joints:
            # Truncate if longer
            visibility = visibility[:self.num_joints]

        # Compute area (approximated by bounding box)
        valid_joints = visibility > 0
        if not np.any(valid_joints):
            return 0.0

        valid_gt = gt_pose[valid_joints]
        x_min, y_min = np.min(valid_gt, axis=0)
        x_max, y_max = np.max(valid_gt, axis=0)
        area = (x_max - x_min) * (y_max - y_min)

        if area <= 0:
            return 0.0

        # Compute OKS
        oks = 0
        num_visible = 0

        for i in range(self.num_joints):
            if i < len(visibility) and visibility[i] > 0:
                dx = pred_pose[i, 0] - gt_pose[i, 0]
                dy = pred_pose[i, 1] - gt_pose[i, 1]
                d_squared = dx * dx + dy * dy

                # OKS formula
                s = self.oks_sigmas[i]
                oks += np.exp(-d_squared / (2 * s * s * area))
                num_visible += 1

        return oks / max(num_visible, 1)
    
    def evaluate(self):
        """Compute all evaluation metrics"""
        pck_results = self.compute_pck()
        oks_results = self.compute_oks()
        
        results = {
            'PCK': pck_results['PCK'],
            'PCK_per_joint': pck_results['PCK_per_joint'],
            'mOKS': oks_results['mOKS'],
            'joint_names': self.joint_names
        }
        
        return results
    
    def print_results(self, results):
        """Print evaluation results in a nice format"""
        print("\n" + "="*60)
        print("PoseTrack21 Evaluation Results")
        print("="*60)
        
        print(f"Overall PCK: {results['PCK']:.3f}")
        print(f"Mean OKS: {results['mOKS']:.3f}")
        
        print("\nPer-joint PCK:")
        for i, (joint_name, pck) in enumerate(zip(results['joint_names'], results['PCK_per_joint'])):
            print(f"  {joint_name:15}: {pck:.3f}")
        
        print("="*60)
