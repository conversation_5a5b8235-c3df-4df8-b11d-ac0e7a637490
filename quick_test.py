#!/usr/bin/env python3

import os
import sys
import json

# Quick test of PoseTrack21 data structure
data_root = 'datasets/PoseTrack21'

print("=== Quick PoseTrack21 Test ===")

# Check basic structure
train_json_dir = os.path.join(data_root, 'data', 'posetrack_data', 'train')
train_img_dir = os.path.join(data_root, 'data', 'images', 'train')

print(f"JSON dir exists: {os.path.exists(train_json_dir)}")
print(f"Image dir exists: {os.path.exists(train_img_dir)}")

if os.path.exists(train_json_dir):
    json_files = [f for f in os.listdir(train_json_dir) if f.endswith('.json')]
    print(f"Found {len(json_files)} JSON files")
    
    if json_files:
        # Test first file
        first_json = os.path.join(train_json_dir, json_files[0])
        print(f"Testing: {json_files[0]}")
        
        with open(first_json, 'r') as f:
            data = json.load(f)
        
        print(f"Images: {len(data.get('images', []))}")
        print(f"Annotations: {len(data.get('annotations', []))}")
        
        # Find images with person annotations
        images_with_persons = []
        annotations_list = data.get('annotations', [])

        # Create mapping of image_id to person annotations
        image_to_persons = {}
        for ann in annotations_list:
            if ann.get('category_id') == 1:  # person category
                img_id = ann.get('image_id')
                if img_id not in image_to_persons:
                    image_to_persons[img_id] = []
                image_to_persons[img_id].append(ann)

        print(f"Images with person annotations: {len(image_to_persons)}")

        if image_to_persons:
            # Get first image with annotations
            first_img_id = list(image_to_persons.keys())[0]
            first_img = None
            for img in data.get('images', []):
                if img.get('id') == first_img_id:
                    first_img = img
                    break

            if first_img:
                file_name = first_img.get('file_name', '')
                print(f"First annotated image: {file_name}")

                # Test path resolution
                path1 = os.path.join(data_root, file_name)
                path2 = os.path.join(data_root, 'data', file_name)

                print(f"Path 1: {path1} (exists: {os.path.exists(path1)})")
                print(f"Path 2: {path2} (exists: {os.path.exists(path2)})")

                print(f"Person annotations: {len(image_to_persons[first_img_id])}")

                # Check keypoints
                first_ann = image_to_persons[first_img_id][0]
                keypoints = first_ann.get('keypoints', [])
                print(f"Keypoints length: {len(keypoints)} (should be 51 for 17 joints)")
        else:
            print("No images with person annotations found!")

print("=== Test Complete ===")
