"""
Evaluation script for MPPET-RLE on PoseTrack21
"""

import os
import torch
import torch.nn as nn
import numpy as np
import argparse
import json
from tqdm import tqdm
import logging
from datetime import datetime

from mppet_rle import MPPET_RLE
from datasets.posetrack_dataset import PoseTrackDataset, get_transform
from utils.metrics import PoseTrackingMetrics, compute_posetrack_metrics
from utils.visualization import draw_tracking_results, create_tracking_video
import cv2


def setup_logging(log_dir):
    """Setup logging configuration."""
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, f'evaluation_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    return logging.getLogger(__name__)


def load_model(checkpoint_path, device):
    """Load trained model from checkpoint."""
    model = MPPET_RLE()
    
    if os.path.isfile(checkpoint_path):
        print(f'Loading checkpoint {checkpoint_path}')
        checkpoint = torch.load(checkpoint_path, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
        print(f'Loaded model from epoch {checkpoint.get("epoch", "unknown")}')
    else:
        raise FileNotFoundError(f'Checkpoint {checkpoint_path} not found')
    
    model = model.to(device)
    model.eval()
    
    return model


def evaluate_detection_and_pose(model, dataloader, device, logger):
    """Evaluate detection and pose estimation performance."""
    model.eval()
    
    metrics_calculator = PoseTrackingMetrics()
    all_results = []
    
    with torch.no_grad():
        pbar = tqdm(dataloader, desc='Evaluating Detection & Pose')
        
        for batch_idx, batch_data in enumerate(pbar):
            try:
                # Process batch (simplified for evaluation)
                # In practice, you would need to handle the full image and detection pipeline
                
                # For now, we'll simulate detection results
                # This should be replaced with actual model inference
                
                batch_results = {
                    'batch_idx': batch_idx,
                    'detections': [],
                    'ground_truth': []
                }
                
                all_results.append(batch_results)
                
            except Exception as e:
                logger.error(f"Error in batch {batch_idx}: {str(e)}")
                continue
    
    # Compute metrics
    pose_metrics = metrics_calculator.get_pose_metrics()
    
    logger.info("Detection & Pose Evaluation Results:")
    logger.info(f"AP_pose: {pose_metrics['AP_pose']:.4f}")
    logger.info(f"PCK: {pose_metrics['PCK']:.4f}")
    logger.info(f"Mean OKS: {pose_metrics['mean_OKS']:.4f}")
    
    return pose_metrics, all_results


def evaluate_tracking(model, dataloader, device, logger, output_dir=None):
    """Evaluate tracking performance."""
    model.eval()
    model.reset_tracker()
    
    metrics_calculator = PoseTrackingMetrics()
    all_tracking_results = []
    frame_count = 0
    
    with torch.no_grad():
        pbar = tqdm(dataloader, desc='Evaluating Tracking')
        
        for batch_idx, batch_data in enumerate(pbar):
            try:
                # Process sequence for tracking
                # This is a simplified implementation
                
                # Simulate tracking results
                tracking_results = []
                
                # In practice, you would:
                # 1. Run detection on each frame
                # 2. Run pose estimation on detected persons
                # 3. Run tracking to associate across frames
                
                all_tracking_results.append(tracking_results)
                frame_count += 1
                
            except Exception as e:
                logger.error(f"Error in tracking batch {batch_idx}: {str(e)}")
                continue
    
    # Compute tracking metrics
    tracking_metrics = metrics_calculator.get_tracking_metrics()
    
    logger.info("Tracking Evaluation Results:")
    logger.info(f"MOTA: {tracking_metrics['MOTA']:.4f}")
    logger.info(f"MOTP: {tracking_metrics['MOTP']:.4f}")
    logger.info(f"ID Switches: {tracking_metrics['ID_switches']}")
    logger.info(f"Fragmentations: {tracking_metrics['Fragmentations']}")
    
    return tracking_metrics, all_tracking_results


def save_results(results, output_path):
    """Save evaluation results to JSON file."""
    # Convert numpy arrays to lists for JSON serialization
    def convert_numpy(obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, dict):
            return {key: convert_numpy(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy(item) for item in obj]
        else:
            return obj
    
    results_serializable = convert_numpy(results)
    
    with open(output_path, 'w') as f:
        json.dump(results_serializable, f, indent=2)
    
    print(f'Results saved to {output_path}')


def create_demo_video(model, dataset, device, output_path, max_frames=100):
    """Create demo video with tracking results."""
    model.eval()
    model.reset_tracker()
    
    frames = []
    tracking_results_list = []
    
    with torch.no_grad():
        for i in tqdm(range(min(len(dataset), max_frames)), desc='Creating demo video'):
            try:
                # Get sample
                sample = dataset[i]
                
                # Simulate processing
                # In practice, you would run the full pipeline here
                
                # Create dummy frame and tracking results for demo
                dummy_frame = np.zeros((480, 640, 3), dtype=np.uint8)
                dummy_tracking_results = []
                
                frames.append(dummy_frame)
                tracking_results_list.append(dummy_tracking_results)
                
            except Exception as e:
                print(f"Error processing frame {i}: {str(e)}")
                continue
    
    if len(frames) > 0:
        create_tracking_video(frames, tracking_results_list, output_path)
        print(f'Demo video saved to {output_path}')


def main():
    parser = argparse.ArgumentParser(description='Evaluate MPPET-RLE')
    parser.add_argument('--checkpoint', type=str, required=True,
                       help='Path to model checkpoint')
    parser.add_argument('--data_root', type=str, default='datasets/PoseTrack21/data',
                       help='Path to PoseTrack21 dataset')
    parser.add_argument('--split', type=str, default='val', choices=['val', 'test'],
                       help='Dataset split to evaluate')
    parser.add_argument('--batch_size', type=int, default=1, help='Batch size')
    parser.add_argument('--output_dir', type=str, default='evaluation_results',
                       help='Output directory for results')
    parser.add_argument('--log_dir', type=str, default='logs', help='Log directory')
    parser.add_argument('--device', type=str, default='cuda', help='Device to use')
    parser.add_argument('--eval_detection', action='store_true',
                       help='Evaluate detection and pose estimation')
    parser.add_argument('--eval_tracking', action='store_true',
                       help='Evaluate tracking performance')
    parser.add_argument('--create_demo', action='store_true',
                       help='Create demo video')
    parser.add_argument('--demo_frames', type=int, default=100,
                       help='Number of frames for demo video')
    
    args = parser.parse_args()
    
    # Setup directories
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Setup logging
    logger = setup_logging(args.log_dir)
    logger.info(f'Starting evaluation with args: {args}')
    
    # Device
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    logger.info(f'Using device: {device}')
    
    # Load model
    model = load_model(args.checkpoint, device)
    logger.info('Model loaded successfully')
    
    # Dataset
    transform = get_transform(args.split)
    dataset = PoseTrackDataset(
        data_root=args.data_root,
        split=args.split,
        transform=transform,
        sequence_length=2  # For tracking evaluation
    )
    
    dataloader = torch.utils.data.DataLoader(
        dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=2,
        pin_memory=True
    )
    
    logger.info(f'Dataset: {len(dataset)} samples')
    
    # Evaluation results
    all_results = {}
    
    # Evaluate detection and pose estimation
    if args.eval_detection:
        logger.info('Starting detection and pose evaluation...')
        pose_metrics, detection_results = evaluate_detection_and_pose(
            model, dataloader, device, logger
        )
        all_results['pose_metrics'] = pose_metrics
        all_results['detection_results'] = detection_results
    
    # Evaluate tracking
    if args.eval_tracking:
        logger.info('Starting tracking evaluation...')
        tracking_metrics, tracking_results = evaluate_tracking(
            model, dataloader, device, logger, args.output_dir
        )
        all_results['tracking_metrics'] = tracking_metrics
        all_results['tracking_results'] = tracking_results
    
    # Save results
    if all_results:
        results_path = os.path.join(args.output_dir, 'evaluation_results.json')
        save_results(all_results, results_path)
    
    # Create demo video
    if args.create_demo:
        logger.info('Creating demo video...')
        demo_path = os.path.join(args.output_dir, 'demo_tracking.mp4')
        create_demo_video(model, dataset, device, demo_path, args.demo_frames)
    
    logger.info('Evaluation completed!')
    
    # Print summary
    if 'pose_metrics' in all_results:
        print("\n=== Pose Estimation Results ===")
        for key, value in all_results['pose_metrics'].items():
            print(f"{key}: {value:.4f}")
    
    if 'tracking_metrics' in all_results:
        print("\n=== Tracking Results ===")
        for key, value in all_results['tracking_metrics'].items():
            print(f"{key}: {value}")


if __name__ == '__main__':
    main()
