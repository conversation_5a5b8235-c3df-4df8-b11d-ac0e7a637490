"""
Test script to verify MPPET-RLE implementation
"""

import torch
import numpy as np
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_rle_pose():
    """Test RLE pose estimation module."""
    print("Testing RLE Pose Estimation...")
    
    try:
        from RLE_pose import RegressFlow
        
        # Create model config
        config = {
            'NUM_LAYERS': 50,
            'NUM_FC_FILTERS': [1024],  # Simplified FC layers
            'HIDDEN_LIST': [256, 256],
            'PRESET': {
                'NUM_JOINTS': 17,
                'IMAGE_SIZE': [256, 192]
            }
        }
        
        # Create model
        model = RegressFlow(**config)
        model.eval()
        
        # Test input
        batch_size = 2
        input_tensor = torch.randn(batch_size, 3, 256, 192)
        
        # Forward pass
        with torch.no_grad():
            output = model(input_tensor)
        
        # Check outputs
        assert output.pred_jts.shape == (batch_size, 17, 2), f"Expected pose shape {(batch_size, 17, 2)}, got {output.pred_jts.shape}"
        assert output.sigma.shape == (batch_size, 17, 2), f"Expected sigma shape {(batch_size, 17, 2)}, got {output.sigma.shape}"
        assert output.maxvals.shape == (batch_size, 17, 1), f"Expected maxvals shape {(batch_size, 17, 1)}, got {output.maxvals.shape}"
        
        print("✓ RLE Pose Estimation test passed")
        return True
        
    except Exception as e:
        print(f"✗ RLE Pose Estimation test failed: {str(e)}")
        return False


def test_detection():
    """Test detection module."""
    print("Testing Detection Module...")
    
    try:
        from detection.detector import FasterRCNNDetector
        
        # Create detector
        detector = FasterRCNNDetector(pretrained=False)  # Use random weights for testing
        detector.eval()
        
        # Test input
        batch_size = 2
        images = [torch.randn(3, 480, 640) for _ in range(batch_size)]
        
        # Forward pass
        with torch.no_grad():
            detections = detector(images)
        
        # Check outputs
        assert len(detections) == batch_size, f"Expected {batch_size} detection results, got {len(detections)}"
        
        for detection in detections:
            assert 'boxes' in detection, "Detection should contain 'boxes'"
            assert 'scores' in detection, "Detection should contain 'scores'"
            assert 'labels' in detection, "Detection should contain 'labels'"
        
        print("✓ Detection Module test passed")
        return True
        
    except Exception as e:
        print(f"✗ Detection Module test failed: {str(e)}")
        return False


def test_tracking():
    """Test tracking module."""
    print("Testing Tracking Module...")
    
    try:
        from tracking.tracker import MPPETTracker
        
        # Create tracker
        tracker = MPPETTracker()
        
        # Create dummy detections
        detections = [
            {
                'bbox': np.array([100, 100, 200, 300]),
                'pose_mu': np.random.rand(17, 2) * 100 + 150,
                'pose_sigma': np.random.rand(17, 2) * 2 + 1,
                'score': 0.9
            },
            {
                'bbox': np.array([300, 150, 400, 350]),
                'pose_mu': np.random.rand(17, 2) * 100 + 350,
                'pose_sigma': np.random.rand(17, 2) * 2 + 1,
                'score': 0.8
            }
        ]
        
        # Process detections
        tracking_results = tracker(detections)
        
        # Check outputs
        assert isinstance(tracking_results, list), "Tracking results should be a list"
        
        for result in tracking_results:
            assert 'track_id' in result, "Track result should contain 'track_id'"
            assert 'bbox' in result, "Track result should contain 'bbox'"
            assert 'pose' in result, "Track result should contain 'pose'"
            assert 'confidence' in result, "Track result should contain 'confidence'"
        
        print("✓ Tracking Module test passed")
        return True
        
    except Exception as e:
        print(f"✗ Tracking Module test failed: {str(e)}")
        return False


def test_mppet_rle():
    """Test full MPPET-RLE model."""
    print("Testing MPPET-RLE Model...")
    
    try:
        from mppet_rle import MPPET_RLE
        
        # Create model
        model = MPPET_RLE()
        model.eval()
        
        # Test detection mode
        batch_size = 1
        images = [torch.randn(3, 480, 640) for _ in range(batch_size)]
        
        with torch.no_grad():
            # Test detection mode
            detection_results = model(images, mode='detect')
            assert len(detection_results) == batch_size, f"Expected {batch_size} detection results"
            
            # Test tracking mode
            tracking_results = model(images, mode='track')
            assert len(tracking_results) == batch_size, f"Expected {batch_size} tracking results"
        
        print("✓ MPPET-RLE Model test passed")
        return True
        
    except Exception as e:
        print(f"✗ MPPET-RLE Model test failed: {str(e)}")
        return False


def test_loss():
    """Test RLE loss function."""
    print("Testing RLE Loss...")
    
    try:
        from RLE_regression_loss import RLELoss
        from easydict import EasyDict
        
        # Create loss function
        criterion = RLELoss()
        
        # Create dummy outputs and targets
        batch_size = 2
        num_joints = 17
        
        output = EasyDict({
            'pred_jts': torch.randn(batch_size, num_joints, 2),
            'sigma': torch.rand(batch_size, num_joints, 2) + 0.1,
            'nf_loss': torch.randn(batch_size, num_joints, 1)
        })
        
        targets = {
            'target_uv': torch.randn(batch_size, num_joints, 2),
            'target_uv_weight': torch.ones(batch_size, num_joints, 2)
        }
        
        # Compute loss
        loss = criterion(output, targets)
        
        # Check loss
        assert isinstance(loss, torch.Tensor), "Loss should be a tensor"
        assert loss.dim() == 0, "Loss should be a scalar"
        assert not torch.isnan(loss), "Loss should not be NaN"
        
        print("✓ RLE Loss test passed")
        return True
        
    except Exception as e:
        print(f"✗ RLE Loss test failed: {str(e)}")
        return False


def test_visualization():
    """Test visualization utilities."""
    print("Testing Visualization...")
    
    try:
        from utils.visualization import draw_pose_with_uncertainty, draw_tracking_results
        
        # Create dummy data
        image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        pose_mu = np.random.rand(17, 2) * 200 + 100
        pose_sigma = np.random.rand(17, 2) * 2 + 1
        
        # Test pose visualization
        vis_image = draw_pose_with_uncertainty(image, pose_mu, pose_sigma)
        assert vis_image.shape == image.shape, "Visualization should preserve image shape"
        
        # Test tracking visualization
        tracking_results = [
            {
                'track_id': 1,
                'bbox': [100, 100, 200, 300],
                'pose': pose_mu,
                'pose_sigma': pose_sigma
            }
        ]
        
        track_image = draw_tracking_results(image, tracking_results)
        assert track_image.shape == image.shape, "Track visualization should preserve image shape"
        
        print("✓ Visualization test passed")
        return True
        
    except Exception as e:
        print(f"✗ Visualization test failed: {str(e)}")
        return False


def test_metrics():
    """Test metrics calculation."""
    print("Testing Metrics...")
    
    try:
        from utils.metrics import PoseTrackingMetrics
        
        # Create metrics calculator
        metrics = PoseTrackingMetrics()
        
        # Test pose metrics
        gt_poses = [np.random.rand(17, 2) * 200]
        pred_poses = [np.random.rand(17, 2) * 200]
        gt_bboxes = [np.array([100, 100, 200, 300])]
        pred_bboxes = [np.array([105, 105, 205, 305])]
        
        metrics.update_pose_metrics(gt_poses, pred_poses, gt_bboxes, pred_bboxes)
        
        # Test tracking metrics
        gt_tracks = [{'track_id': 1, 'bbox': [100, 100, 200, 300]}]
        pred_tracks = [{'track_id': 1, 'bbox': [105, 105, 205, 305]}]
        
        metrics.update_tracking_metrics(gt_tracks, pred_tracks, frame_id=0)
        
        # Get metrics
        pose_metrics = metrics.get_pose_metrics()
        tracking_metrics = metrics.get_tracking_metrics()
        
        # Check metrics
        assert 'AP_pose' in pose_metrics, "Should contain AP_pose"
        assert 'MOTA' in tracking_metrics, "Should contain MOTA"
        
        print("✓ Metrics test passed")
        return True
        
    except Exception as e:
        print(f"✗ Metrics test failed: {str(e)}")
        return False


def main():
    """Run all tests."""
    print("Running MPPET-RLE Tests...\n")
    
    tests = [
        test_loss,
        test_rle_pose,
        test_detection,
        test_tracking,
        test_mppet_rle,
        test_visualization,
        test_metrics
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! MPPET-RLE implementation is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
